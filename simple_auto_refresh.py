#!/usr/bin/env python3
"""
简单的自动刷新实现
使用Streamlit推荐的方法
"""

import streamlit as st
import time
from datetime import datetime

# 页面配置
st.set_page_config(
    page_title="简单自动刷新",
    page_icon="🔄",
    layout="wide"
)

def main():
    """主函数"""
    st.title("🔄 简单自动刷新演示")
    
    # 初始化session state
    if 'counter' not in st.session_state:
        st.session_state.counter = 0
    if 'auto_refresh' not in st.session_state:
        st.session_state.auto_refresh = False
    
    # 控制面板
    col1, col2 = st.columns(2)
    
    with col1:
        auto_refresh = st.checkbox("🔄 启用自动刷新", value=st.session_state.auto_refresh)
        st.session_state.auto_refresh = auto_refresh
    
    with col2:
        if st.button("🔄 手动刷新"):
            st.session_state.counter += 1
            st.rerun()
    
    # 显示信息
    st.subheader("📊 实时信息")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("当前时间", datetime.now().strftime("%H:%M:%S"))
    
    with col2:
        st.metric("刷新计数", st.session_state.counter)
    
    with col3:
        st.metric("随机数", int(time.time()) % 100)
    
    # 自动刷新逻辑
    if st.session_state.auto_refresh:
        st.info("🔄 自动刷新已启用，每5秒刷新一次")
        
        # 使用占位符显示倒计时
        placeholder = st.empty()
        
        for i in range(5, 0, -1):
            placeholder.info(f"⏰ {i}秒后自动刷新...")
            time.sleep(1)
        
        # 更新计数器并刷新
        st.session_state.counter += 1
        placeholder.success("🔄 正在刷新...")
        st.rerun()
    else:
        st.info("⏸️ 自动刷新已禁用")

if __name__ == "__main__":
    main()
