#!/usr/bin/env python3
"""
Jenkins Stage监控功能演示
展示整体构建状态和当前Stage信息的标签页显示
"""

import streamlit as st
import time
import random
from datetime import datetime

# 页面配置
st.set_page_config(
    page_title="Jenkins Stage监控演示",
    page_icon="🔄",
    layout="wide",
    initial_sidebar_state="expanded"
)

def mock_jenkins_status(job_name: str, build_number: int, elapsed_time: int):
    """模拟Jenkins job状态"""
    if elapsed_time < 60:
        status = "RUNNING"
        building = True
        duration = 0
    else:
        status = "SUCCESS" if random.random() > 0.3 else "FAILURE"
        building = False
        duration = elapsed_time * 1000
    
    return {
        "success": True,
        "job_name": job_name,
        "build_number": build_number,
        "status": status,
        "building": building,
        "duration": duration,
        "url": f"https://jenkins.example.com/job/{job_name}/{build_number}/"
    }

def mock_pipeline_stages(job_name: str, build_number: int, elapsed_time: int):
    """模拟Pipeline stages信息"""
    # 定义所有stages
    all_stages = [
        {"name": "Checkout", "duration": 10},
        {"name": "Build", "duration": 30},
        {"name": "Test", "duration": 45},
        {"name": "Package", "duration": 15},
        {"name": "Deploy", "duration": 20}
    ]
    
    stages = []
    current_stage = None
    cumulative_time = 0
    
    for i, stage_def in enumerate(all_stages):
        stage_duration = stage_def["duration"]
        stage_start = cumulative_time
        stage_end = cumulative_time + stage_duration
        
        if elapsed_time < stage_start:
            # 还未开始的stage
            stage_status = "NOT_STARTED"
        elif elapsed_time < stage_end:
            # 正在进行的stage
            stage_status = "IN_PROGRESS"
            current_stage = {
                "id": f"stage_{i}",
                "name": stage_def["name"],
                "status": stage_status,
                "start_time": stage_start * 1000,
                "duration": (elapsed_time - stage_start) * 1000,
                "pause_duration": 0
            }
        else:
            # 已完成的stage
            stage_status = "SUCCESS" if random.random() > 0.1 else "SUCCESS"  # 大部分成功
        
        stage_info = {
            "id": f"stage_{i}",
            "name": stage_def["name"],
            "status": stage_status,
            "start_time": stage_start * 1000,
            "duration": min(stage_duration * 1000, max(0, (elapsed_time - stage_start) * 1000)),
            "pause_duration": 0
        }
        
        stages.append(stage_info)
        cumulative_time += stage_duration
    
    # 如果没有当前stage，可能是最后一个
    if not current_stage and elapsed_time >= cumulative_time:
        current_stage = stages[-1] if stages else None
    
    return {
        "success": True,
        "job_name": job_name,
        "build_number": build_number,
        "stages": stages,
        "current_stage": current_stage,
        "total_stages": len(stages),
        "parsed_from_console": False
    }

def mock_console_output(elapsed_time: int):
    """模拟控制台输出"""
    base_logs = [
        "[Pipeline] Start of Pipeline",
        "[Pipeline] stage",
        "[Pipeline] { (Checkout)",
        "Checking out code from repository...",
        "Cloning repository https://github.com/example/repo.git",
        "[Pipeline] }",
        "[Pipeline] stage",
        "[Pipeline] { (Build)",
        "Starting build process...",
        "Compiling source code...",
        "Build completed successfully",
        "[Pipeline] }",
        "[Pipeline] stage", 
        "[Pipeline] { (Test)",
        "Running unit tests...",
        "Test results: 45 passed, 0 failed",
        "[Pipeline] }",
        "[Pipeline] stage",
        "[Pipeline] { (Package)",
        "Creating deployment package...",
        "Package created: app-v1.2.3.tar.gz"
    ]
    
    # 根据时间返回相应的日志
    lines_to_show = min(len(base_logs), elapsed_time // 3)
    current_logs = base_logs[:lines_to_show]
    
    # 添加时间戳
    timestamped_logs = []
    for i, log in enumerate(current_logs):
        timestamp = datetime.now().strftime("%H:%M:%S")
        timestamped_logs.append(f"[{timestamp}] {log}")
    
    # 只返回最新20行
    if len(timestamped_logs) > 20:
        timestamped_logs = timestamped_logs[-20:]
    
    return "\n".join(timestamped_logs)

def display_stage_monitoring():
    """显示Stage监控演示"""
    job_info = st.session_state.demo_monitoring_job
    
    if not job_info:
        return
    
    # 计算经过时间
    elapsed_time = int(time.time() - st.session_state.demo_start_time) if st.session_state.demo_start_time else 0
    
    st.divider()
    st.subheader(f"📊 {job_info['job_name']} Build {job_info['build_number']}")
    
    # 显示最后更新时间
    st.caption(f"最后更新: {datetime.now().strftime('%H:%M:%S')} | 运行时间: {elapsed_time}秒")
    
    # 获取模拟数据
    status_info = mock_jenkins_status(job_info['job_name'], job_info['build_number'], elapsed_time)
    stages_info = mock_pipeline_stages(job_info['job_name'], job_info['build_number'], elapsed_time)
    console_output = mock_console_output(elapsed_time)
    
    # 使用标签页显示不同信息
    tab1, tab2 = st.tabs(["📊 整体构建状态", "🔄 当前Stage"])
    
    with tab1:
        # 整体构建状态信息
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            status = status_info.get("status", "未知")
            if status == "SUCCESS":
                st.success(f"✅ {status}")
            elif status == "FAILURE":
                st.error(f"❌ {status}")
            elif status == "RUNNING":
                st.info(f"🔄 {status}")
            else:
                st.warning(f"⚠️ {status}")
        
        with col2:
            building = status_info.get("building", False)
            if building:
                st.info("🔄 构建中")
            else:
                st.success("✅ 已完成")
        
        with col3:
            duration = status_info.get("duration", 0)
            if duration > 0:
                duration_sec = duration / 1000
                st.metric("持续时间", f"{duration_sec:.1f}秒")
            else:
                st.metric("持续时间", f"{elapsed_time}秒")
        
        with col4:
            url = status_info.get("url", "")
            if url:
                st.markdown(f"[🔗 查看详情]({url})")
    
    with tab2:
        # 当前Stage信息
        if stages_info.get("success"):
            current_stage = stages_info.get("current_stage")
            all_stages = stages_info.get("stages", [])
            total_stages = stages_info.get("total_stages", 0)
            
            # 显示当前stage
            if current_stage:
                st.subheader(f"🎯 当前Stage: {current_stage['name']}")
                
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    stage_status = current_stage.get("status", "未知")
                    if stage_status == "SUCCESS":
                        st.success(f"✅ {stage_status}")
                    elif stage_status == "FAILURE":
                        st.error(f"❌ {stage_status}")
                    elif stage_status == "IN_PROGRESS":
                        st.info(f"🔄 {stage_status}")
                    else:
                        st.warning(f"⚠️ {stage_status}")
                
                with col2:
                    stage_duration = current_stage.get("duration", 0)
                    if stage_duration > 0:
                        duration_sec = stage_duration / 1000
                        st.metric("Stage持续时间", f"{duration_sec:.1f}秒")
                    else:
                        st.metric("Stage持续时间", "进行中...")
                
                with col3:
                    st.metric("总Stage数", f"{total_stages}")
            
            # 显示所有stages的进度
            if all_stages:
                st.subheader("📋 所有Stages")
                
                for i, stage in enumerate(all_stages):
                    stage_name = stage.get("name", f"Stage {i+1}")
                    stage_status = stage.get("status", "未知")
                    
                    # 使用不同的图标表示不同状态
                    if stage_status == "SUCCESS":
                        icon = "✅"
                    elif stage_status == "FAILURE":
                        icon = "❌"
                    elif stage_status == "IN_PROGRESS":
                        icon = "🔄"
                    else:
                        icon = "⏳"
                    
                    st.write(f"{icon} **{stage_name}** - {stage_status}")
        else:
            st.warning("⚠️ 无法获取Stage信息")
    
    # 控制台输出 (最新20行)
    st.subheader("📝 最新日志 (最近20行)")
    
    if console_output:
        st.code(console_output, language="text", line_numbers=True)
    else:
        st.info("暂无日志输出")

def main():
    """主函数"""
    st.title("🚀 Jenkins Stage监控功能演示")
    st.markdown("展示整体构建状态和当前Stage信息的标签页显示")
    
    # 初始化session state
    if 'demo_monitoring_job' not in st.session_state:
        st.session_state.demo_monitoring_job = None
    if 'demo_auto_refresh' not in st.session_state:
        st.session_state.demo_auto_refresh = False
    if 'demo_start_time' not in st.session_state:
        st.session_state.demo_start_time = None
    
    # 功能说明
    with st.expander("📖 新功能说明", expanded=True):
        st.markdown("""
        ### ✨ 新增功能：
        
        1. **标签页设计**: 
           - 第一个标签显示整体构建状态
           - 第二个标签显示当前处于哪个Stage
        
        2. **Stage信息**:
           - 当前正在执行的Stage名称和状态
           - 所有Stages的进度列表
           - Stage持续时间统计
        
        3. **智能解析**:
           - 优先使用Jenkins Workflow API
           - 降级到控制台日志解析
           - 支持各种Pipeline格式
        
        ### 🎯 使用场景：
        - 监控复杂的CI/CD Pipeline
        - 快速定位当前执行阶段
        - 了解整体构建进度
        """)
    
    # Job输入区域
    st.subheader("🔍 Job配置")
    col1, col2, col3 = st.columns([3, 2, 2])
    
    with col1:
        job_name = st.text_input("Job名称", value="demo-pipeline-job", key="demo_job_name")
    
    with col2:
        build_number = st.number_input("Build号", min_value=1, value=456, key="demo_build_number")
    
    with col3:
        st.write("")
        auto_refresh = st.checkbox("🔄 自动刷新 (5秒)", value=st.session_state.demo_auto_refresh)
        st.session_state.demo_auto_refresh = auto_refresh
    
    # 控制按钮
    col1, col2, col3 = st.columns([2, 2, 2])
    
    with col1:
        if st.button("🚀 开始监控", type="primary", key="demo_start"):
            if job_name:
                st.session_state.demo_monitoring_job = {
                    "job_name": job_name,
                    "build_number": build_number
                }
                st.session_state.demo_start_time = time.time()
                st.success(f"✅ 开始监控 {job_name} Build {build_number}")
            else:
                st.error("请输入Job名称")
    
    with col2:
        if st.button("🔄 手动刷新", key="demo_refresh"):
            if st.session_state.demo_monitoring_job:
                st.success("🔄 数据已刷新")
                st.rerun()
            else:
                st.warning("请先开始监控一个Job")
    
    with col3:
        if st.button("⏹️ 停止监控", key="demo_stop"):
            st.session_state.demo_monitoring_job = None
            st.session_state.demo_auto_refresh = False
            st.session_state.demo_start_time = None
            st.success("⏹️ 已停止监控")
    
    # 显示监控结果
    if st.session_state.demo_monitoring_job:
        display_stage_monitoring()
        
        # 自动刷新逻辑
        if st.session_state.demo_auto_refresh:
            time.sleep(0.1)
            st.rerun()
    else:
        st.info("💡 请输入Job名称和Build号，然后点击'开始监控'来体验Stage监控功能")

if __name__ == "__main__":
    main()
