# ✅ 计划审批时间筛选功能说明

## 📋 功能概述

在计划审批页面新增了智能时间筛选功能，能够按部署日期和时间自动分类任务，将到期任务和未来任务分开显示，并给出相应的提示，帮助用户优先处理紧急任务。

## ✨ 主要功能

### 1. 智能时间筛选
- **自动分类**: 根据部署日期和时间自动将任务分为三类
- **实时筛选**: 基于当前时间或用户自定义时间进行筛选
- **动态更新**: 时间筛选条件变化时实时更新任务分类

### 2. 任务分类显示

#### 🔴 到期任务 (需要立即处理)
- **定义**: 部署时间已到或已过期的任务
- **显示**: 红色警告样式，置顶显示
- **提示**: "⚠️ 有 X 个任务已到部署时间，需要立即处理！"

#### 🟡 未来任务
- **定义**: 计划在未来执行的任务
- **显示**: 蓝色信息样式，可折叠显示
- **提示**: "ℹ️ 有 X 个任务计划在未来执行"

#### ⚪ 无时间信息任务
- **定义**: 没有明确部署时间的任务（如多租户服务）
- **显示**: 灰色信息样式，可折叠显示
- **提示**: "ℹ️ 有 X 个任务没有明确的时间信息"

### 3. 交互式筛选控制

#### 时间筛选设置
```python
# 筛选基准日期
filter_date = st.date_input("筛选基准日期", value=current_date)

# 筛选基准时间  
filter_time = st.time_input("筛选基准时间", value=current_time)
```

#### 统计信息显示
```python
col1, col2, col3 = st.columns(3)
with col1:
    st.metric("🔴 到期任务", len(due_tasks))
with col2:
    st.metric("🟡 未来任务", len(future_tasks))  
with col3:
    st.metric("⚪ 无时间信息", len(no_time_tasks))
```

## 🔧 技术实现

### 1. 数据解析

#### 发布计划表格解析
```python
def parse_deployment_plan_table(deployment_plan_text):
    """解析发布计划表格文本"""
    lines = deployment_plan_text.strip().split('\n')
    plan_items = []
    
    # 找到表格开始位置
    table_start = -1
    for i, line in enumerate(lines):
        if '| 计划部署日期 |' in line:
            table_start = i + 2  # 跳过表头和分隔线
            break
    
    # 解析表格行
    for line in lines[table_start:]:
        columns = [col.strip() for col in line.split('|')[1:-1]]
        if len(columns) >= 6:
            plan_items.append({
                '计划部署日期': columns[0],
                '时间窗口': columns[1],
                '客户名': columns[2],
                '租户名': columns[3],
                'Service名': columns[4],
                '是否部署PS代码': columns[5]
            })
    
    return plan_items
```

#### 时间解析逻辑
```python
def parse_task_datetime(item):
    """解析任务的日期时间"""
    date_str = item.get('计划部署日期', '').strip()
    time_str = item.get('时间窗口', '').strip()
    
    if not date_str or not time_str:
        return None
    
    # 解析日期 (格式: 2025-06-22)
    date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
    
    # 解析时间窗口 (格式: 17:00-19:00)
    if '-' in time_str:
        start_time_str = time_str.split('-')[0].strip()
    else:
        start_time_str = time_str
    
    time_obj = datetime.strptime(start_time_str, '%H:%M').time()
    
    # 合并日期和时间
    return datetime.combine(date_obj, time_obj)
```

### 2. 任务分类逻辑

```python
def display_filtered_deployment_plan(data):
    """显示按时间筛选的发布计划"""
    # 解析发布计划数据
    plan_items = parse_deployment_plan_table(deployment_plan_text)
    
    # 获取筛选时间
    filter_datetime = datetime.combine(filter_date, filter_time)
    
    # 分类任务
    due_tasks = []      # 到期的任务
    future_tasks = []   # 未来的任务
    no_time_tasks = []  # 没有时间信息的任务
    
    for item in plan_items:
        task_datetime = parse_task_datetime(item)
        
        if task_datetime is None:
            no_time_tasks.append(item)
        elif task_datetime <= filter_datetime:
            due_tasks.append(item)
        else:
            future_tasks.append(item)
```

### 3. 界面显示

#### 任务表格显示
```python
def display_task_table(tasks, task_type):
    """显示任务表格"""
    df = pd.DataFrame(tasks)
    
    # 根据任务类型设置样式
    if task_type == "due":
        st.error("⚠️ 以下任务已到部署时间，请立即处理：")
    elif task_type == "future":
        st.info("📅 以下任务计划在未来执行：")
    else:
        st.info("📋 以下任务没有明确的时间信息：")
    
    # 显示表格
    st.dataframe(df, use_container_width=True, hide_index=True)
```

## 📊 用户界面

### 1. 页面布局
```
✅ 计划审批
├── 🕒 时间筛选设置
│   ├── 筛选基准日期 [日期选择器]
│   └── 筛选基准时间 [时间选择器]
├── 📊 任务统计
│   ├── 🔴 到期任务: X个
│   ├── 🟡 未来任务: X个
│   └── ⚪ 无时间信息: X个
├── 🔴 到期任务 (需要立即处理)
│   ├── ⚠️ 警告提示
│   └── 📋 任务表格
├── 🟡 未来任务 [可折叠]
│   ├── ℹ️ 信息提示
│   └── 📋 任务表格
└── ⚪ 无时间信息任务 [可折叠]
    ├── ℹ️ 信息提示
    └── 📋 任务表格
```

### 2. 视觉设计

#### 颜色编码
- **🔴 红色**: 到期任务，紧急处理
- **🟡 黄色**: 未来任务，计划执行
- **⚪ 灰色**: 无时间信息，常规处理

#### 交互设计
- **折叠面板**: 未来任务和无时间信息任务默认折叠
- **实时更新**: 筛选条件变化时立即更新显示
- **响应式布局**: 适配不同屏幕尺寸

## 🎯 使用场景

### 1. 日常审批工作
- **快速识别**: 一眼看出哪些任务需要立即处理
- **优先级管理**: 按时间紧急程度安排审批顺序
- **避免遗漏**: 确保不会错过到期的部署任务

### 2. 时间管理
- **工作计划**: 了解未来的部署安排
- **资源调配**: 根据任务时间安排人力资源
- **风险控制**: 提前识别可能的时间冲突

### 3. 审批决策
- **紧急处理**: 优先审批到期任务
- **批量操作**: 对同类任务进行批量处理
- **状态跟踪**: 实时了解任务执行状态

## 🚀 使用方法

### 1. 基本操作
1. 进入"计划审批"页面
2. 查看时间筛选设置（默认为当前时间）
3. 查看任务统计信息
4. 优先处理红色标记的到期任务

### 2. 自定义筛选
1. 调整"筛选基准日期"
2. 调整"筛选基准时间"
3. 观察任务分类的实时变化
4. 根据需要处理相应任务

### 3. 审批操作
1. 查看到期任务详情
2. 点击"审批通过"或"拒绝"
3. 处理完成后刷新页面
4. 继续处理其他任务

## 📈 效果评估

### 1. 效率提升
- **快速定位**: 减少查找到期任务的时间
- **优先处理**: 确保紧急任务得到及时处理
- **批量操作**: 提高审批处理效率

### 2. 风险降低
- **避免遗漏**: 显著降低遗漏到期任务的风险
- **时间管理**: 更好的时间规划和资源分配
- **质量保证**: 确保部署任务按时执行

### 3. 用户体验
- **直观显示**: 清晰的视觉区分和提示
- **操作便捷**: 简单的交互和快速的响应
- **信息完整**: 全面的任务信息和状态显示

---

**功能版本**: v3.1.0  
**实现日期**: 2025年7月1日  
**状态**: ✅ 已完成并测试
