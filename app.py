"""
Streamlit Web界面
使用Streamlit创建简单的web界面供用户交互
"""

import streamlit as st
import pandas as pd
import time
import logging
from typing import Dict, Any, List
import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.release_agent import ReleaseAgent
from src.llm_config import llm_config_manager, LLMProvider

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 页面配置
st.set_page_config(
    page_title="智能发布计划助手",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 初始化session state
if 'agent' not in st.session_state:
    st.session_state.agent = ReleaseAgent()
if 'deployment_plan' not in st.session_state:
    st.session_state.deployment_plan = None
if 'jenkins_jobs' not in st.session_state:
    st.session_state.jenkins_jobs = None
if 'execution_status' not in st.session_state:
    st.session_state.execution_status = None
if 'monitoring_jobs' not in st.session_state:
    st.session_state.monitoring_jobs = {}

def main():
    """主函数"""
    st.title("🚀 智能发布计划助手")
    st.markdown("基于LangChain和LangGraph的发布计划管理系统")

    # 侧边栏
    with st.sidebar:
        st.header("📋 操作面板")

        # 环境配置检查
        st.subheader("🔧 环境配置")
        check_environment_config()

        st.divider()

        # LLM配置面板
        st.subheader("🤖 LLM配置")
        llm_config_sidebar()

        st.divider()

        # 快速操作
        st.subheader("⚡ 快速操作")
        if st.button("🔄 重置会话", type="secondary"):
            reset_session()
            st.rerun()

    # 检查是否显示高级LLM配置
    if st.session_state.get('show_advanced_llm_config', False):
        advanced_llm_config_page()
        return

    # 主要内容区域
    tab1, tab2, tab3, tab4 = st.tabs(["📝 发布计划查询", "✅ 计划审批", "🚀 执行管理", "📊 监控面板"])

    with tab1:
        deployment_plan_tab()

    with tab2:
        plan_approval_tab()

    with tab3:
        execution_management_tab()

    with tab4:
        monitoring_dashboard_tab()

def check_environment_config():
    """检查环境配置"""
    config_items = [
        {
            "name": "数据库",
            "env_vars": ["MYSQL_HOST", "MYSQL_USER", "MYSQL_PASSWORD"],
            "optional_vars": ["MYSQL_PORT", "MYSQL_DATABASE"],
            "icon": "🗄️"
        },
        {
            "name": "Jenkins",
            "env_vars": ["JENKINS_URL", "JENKINS_USERNAME", "JENKINS_TOKEN"],
            "optional_vars": [],
            "icon": "🔧"
        }
    ]

    for config in config_items:
        name = config["name"]
        env_vars = config["env_vars"]
        optional_vars = config.get("optional_vars", [])
        icon = config["icon"]

        # 检查必需配置
        all_configured = all(os.getenv(var) for var in env_vars)
        status = "✅" if all_configured else "❌"

        # 创建可展开的配置详情
        with st.expander(f"{status} {icon} {name}", expanded=False):
            if all_configured:
                st.success(f"✅ {name}配置完整")

                # 显示配置详情
                st.subheader("📋 配置详情")

                if name == "数据库":
                    show_database_config(env_vars + optional_vars)
                elif name == "Jenkins":
                    show_jenkins_config(env_vars)

            else:
                st.error(f"❌ {name}配置不完整")
                st.info("缺少以下必需的环境变量：")

                for var in env_vars:
                    if not os.getenv(var):
                        st.write(f"- `{var}`")

                # 显示配置示例
                if name == "数据库":
                    show_database_config_example()
                elif name == "Jenkins":
                    show_jenkins_config_example()

def show_database_config(env_vars):
    """显示数据库配置详情"""
    st.markdown("**连接信息：**")

    for var in env_vars:
        value = os.getenv(var)
        if value:
            if var == "MYSQL_PASSWORD":
                # 密码脱敏显示
                masked_value = "*" * len(value) if len(value) > 0 else "未设置"
                st.write(f"- **{var}**: `{masked_value}`")
            else:
                st.write(f"- **{var}**: `{value}`")
        else:
            st.write(f"- **{var}**: `未设置`")

    # 显示连接字符串（脱敏）
    host = os.getenv("MYSQL_HOST", "未设置")
    port = os.getenv("MYSQL_PORT", "3306")
    database = os.getenv("MYSQL_DATABASE", "未设置")
    user = os.getenv("MYSQL_USER", "未设置")

    if host != "未设置" and user != "未设置":
        connection_string = f"mysql://{user}:***@{host}:{port}/{database}"
        st.markdown(f"**连接字符串**: `{connection_string}`")

    # 测试连接按钮
    if st.button("🔍 测试数据库连接", key="test_db_connection"):
        test_database_connection()

def show_jenkins_config(env_vars):
    """显示Jenkins配置详情"""
    st.markdown("**连接信息：**")

    for var in env_vars:
        value = os.getenv(var)
        if value:
            if var == "JENKINS_TOKEN":
                # Token脱敏显示
                masked_value = value[:8] + "*" * (len(value) - 8) if len(value) > 8 else "*" * len(value)
                st.write(f"- **{var}**: `{masked_value}`")
            else:
                st.write(f"- **{var}**: `{value}`")
        else:
            st.write(f"- **{var}**: `未设置`")

    # 显示Jenkins URL链接
    jenkins_url = os.getenv("JENKINS_URL")
    if jenkins_url:
        st.markdown(f"**Jenkins地址**: [{jenkins_url}]({jenkins_url})")

    # 测试连接按钮
    if st.button("🔍 测试Jenkins连接", key="test_jenkins_connection"):
        test_jenkins_connection()

def show_database_config_example():
    """显示数据库配置示例"""
    st.markdown("**配置示例：**")
    st.code("""
# 数据库配置
MYSQL_HOST=your-database-host
MYSQL_PORT=3306
MYSQL_USER=your-username
MYSQL_PASSWORD=your-password
MYSQL_DATABASE=your-database-name
    """, language="bash")

def show_jenkins_config_example():
    """显示Jenkins配置示例"""
    st.markdown("**配置示例：**")
    st.code("""
# Jenkins配置
JENKINS_URL=https://your-jenkins-url
JENKINS_USERNAME=your-username
JENKINS_TOKEN=your-api-token
    """, language="bash")

def test_database_connection():
    """测试数据库连接"""
    try:
        # 这里可以添加实际的数据库连接测试逻辑
        # 目前显示模拟结果
        with st.spinner("正在测试数据库连接..."):
            import time
            time.sleep(1)  # 模拟连接测试

        host = os.getenv("MYSQL_HOST")
        database = os.getenv("MYSQL_DATABASE", "release")

        if host:
            st.success(f"✅ 数据库连接成功！")
            st.info(f"已连接到: {host}/{database}")
        else:
            st.error("❌ 数据库连接失败：缺少主机配置")

    except Exception as e:
        st.error(f"❌ 数据库连接测试失败: {str(e)}")

def test_jenkins_connection():
    """测试Jenkins连接"""
    try:
        jenkins_url = os.getenv("JENKINS_URL")
        jenkins_user = os.getenv("JENKINS_USERNAME")
        jenkins_token = os.getenv("JENKINS_TOKEN")

        if not all([jenkins_url, jenkins_user, jenkins_token]):
            st.error("❌ Jenkins连接失败：缺少必要配置")
            st.info("请确保在.env文件中配置了JENKINS_URL、JENKINS_USERNAME和JENKINS_TOKEN")
            return

        with st.spinner("正在测试Jenkins连接..."):
            try:
                # 尝试创建Jenkins客户端并测试连接
                from src.jenkins_client import JenkinsClient
                jenkins_client = JenkinsClient()

                # 如果能成功创建客户端，说明连接正常
                st.success(f"✅ Jenkins连接成功！")
                st.info(f"已连接到: {jenkins_url}")
                st.info(f"用户: {jenkins_user}")

                # 显示连接详情
                with st.expander("🔍 连接详情", expanded=False):
                    st.write("**连接参数**:")
                    st.write(f"- URL: `{jenkins_url}`")
                    st.write(f"- 用户名: `{jenkins_user}`")
                    st.write(f"- Token: `{jenkins_token[:8]}***`")
                    st.write("**状态**: 连接正常，可以进行Jenkins操作")

            except Exception as jenkins_error:
                st.error(f"❌ Jenkins连接失败: {str(jenkins_error)}")

                # 显示详细错误信息
                with st.expander("🔍 错误详情", expanded=False):
                    st.code(str(jenkins_error))

                # 提供解决建议
                st.info("""
                💡 **解决建议**:
                1. 检查Jenkins URL是否正确且可访问
                2. 验证用户名和Token是否有效
                3. 确认网络连接正常
                4. 检查Jenkins服务器是否运行正常
                """)

    except Exception as e:
        st.error(f"❌ Jenkins连接测试失败: {str(e)}")

def llm_config_sidebar():
    """侧边栏LLM配置面板"""
    # 当前配置显示
    current_config = llm_config_manager.get_current_config()

    # 显示当前配置状态
    st.write(f"**当前供应商**: {current_config['provider_name']}")
    st.write(f"**当前模型**: {current_config['model']}")

    # 获取可用供应商
    providers = llm_config_manager.get_available_providers()
    provider_options = [p["value"] for p in providers if p["configured"]]

    if not provider_options:
        st.error("❌ 未配置API密钥")
        st.info("请在.env文件中配置API密钥")
        return

    # 供应商选择
    selected_provider = st.selectbox(
        "选择供应商",
        options=provider_options,
        format_func=lambda x: next(p["label"] for p in providers if p["value"] == x),
        index=provider_options.index(current_config["provider"]) if current_config["provider"] in provider_options else 0,
        key="sidebar_provider"
    )

    # 获取模型列表
    try:
        provider_enum = LLMProvider(selected_provider)
        models = llm_config_manager.get_available_models(provider_enum)

        if models:
            model_options = [m["value"] for m in models]
            default_index = 0
            if current_config["model"] in model_options:
                default_index = model_options.index(current_config["model"])

            selected_model = st.selectbox(
                "选择模型",
                options=model_options,
                format_func=lambda x: next(m["label"] for m in models if m["value"] == x),
                index=default_index,
                key="sidebar_model"
            )

            # 应用配置按钮
            if st.button("🔄 应用配置", type="primary", key="sidebar_apply"):
                try:
                    llm_config_manager.set_current_config(selected_provider, selected_model)
                    st.success("✅ 配置已更新")
                    st.rerun()
                except Exception as e:
                    st.error(f"❌ 配置失败: {str(e)}")

            # 测试连接按钮
            if st.button("🧪 测试连接", key="sidebar_test"):
                with st.spinner("测试中..."):
                    test_result = llm_config_manager.test_connection(selected_provider, selected_model)
                    if test_result["success"]:
                        st.success("✅ 连接成功")
                    else:
                        st.error("❌ 连接失败")
        else:
            st.error("❌ 无可用模型")

    except Exception as e:
        st.error(f"❌ 获取模型失败: {str(e)}")

    # 高级配置链接
    if st.button("⚙️ 高级配置", key="sidebar_advanced"):
        st.session_state.show_advanced_llm_config = True

def reset_session():
    """重置会话状态"""
    keys_to_reset = ['deployment_plan', 'jenkins_jobs', 'execution_status', 'monitoring_jobs']
    for key in keys_to_reset:
        if key in st.session_state:
            del st.session_state[key]
    st.session_state.agent = ReleaseAgent()

def deployment_plan_tab():
    """发布计划查询标签页"""
    st.header("📝 发布计划查询")
    
    # 输入区域
    col1, col2 = st.columns(2)
    
    with col1:
        version = st.text_input(
            "版本号",
            placeholder="例如: 25R1.2",
            help="输入发布版本号，格式如 25R1.2"
        )
    
    with col2:
        environment = st.selectbox(
            "环境",
            ["Prod", "Staging", "Test", "Dev"],
            help="选择目标环境"
        )
    
    # 查询按钮
    if st.button("🔍 查询发布计划", type="primary"):
        if version and environment:
            with st.spinner("正在查询发布计划..."):
                query_deployment_plan(version, environment)
        else:
            st.error("请输入版本号和选择环境")
    
    # 显示查询结果
    if st.session_state.deployment_plan:
        display_deployment_plan()

def query_deployment_plan(version: str, environment: str):
    """查询发布计划"""
    try:
        user_input = f"列出 {version} {environment} 的发布计划"
        result = st.session_state.agent.process_user_request(user_input)
        
        if result["success"]:
            st.session_state.deployment_plan = result["data"]
            st.success(result["message"])
        else:
            st.error(result["message"])
            
    except Exception as e:
        st.error(f"查询失败: {str(e)}")

def display_deployment_plan():
    """显示发布计划"""
    data = st.session_state.deployment_plan
    
    st.subheader(f"📋 {data['version']} {data['environment']}环境 发布计划")
    
    # 显示发布计划表格
    if "deployment_plan" in data:
        st.markdown(data["deployment_plan"])
    
    # 显示Jenkins jobs
    if "jenkins_jobs" in data:
        st.subheader("🔧 Jenkins Jobs")
        st.markdown(data["jenkins_jobs"])

def plan_approval_tab():
    """计划审批标签页"""
    st.header("✅ 计划审批")

    if not st.session_state.deployment_plan:
        st.info("请先在「发布计划查询」标签页查询发布计划")
        return

    data = st.session_state.deployment_plan
    workflow_state = data.get("workflow_state", "")

    # 显示按时间筛选的发布计划
    display_filtered_deployment_plan(data)

    st.divider()

    if workflow_state == "waiting_plan_approval":
        st.warning("⏳ 等待审批发布计划")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("✅ 审批通过", type="primary"):
                approve_plan()

        with col2:
            if st.button("❌ 拒绝", type="secondary"):
                reject_plan()

    elif workflow_state == "waiting_execution_approval":
        st.warning("⏳ 等待确认执行")
        display_jenkins_jobs_selection()

    else:
        st.info(f"当前状态: {workflow_state}")

def display_filtered_deployment_plan(data):
    """显示按时间筛选的发布计划"""
    import datetime
    from datetime import datetime as dt

    # 解析发布计划数据
    deployment_plan_text = data.get("deployment_plan", "")
    if not deployment_plan_text:
        st.warning("没有发布计划数据")
        return

    # 解析表格数据
    plan_items = parse_deployment_plan_table(deployment_plan_text)
    if not plan_items:
        st.warning("无法解析发布计划数据")
        return

    # 获取当前时间
    now = dt.now()
    current_date = now.date()
    current_time = now.time()

    # 时间筛选控制
    st.subheader("🕒 时间筛选设置")
    col1, col2 = st.columns(2)

    with col1:
        filter_date = st.date_input(
            "筛选基准日期",
            value=current_date,
            help="选择用于筛选的基准日期"
        )

    with col2:
        filter_time = st.time_input(
            "筛选基准时间",
            value=current_time,
            help="选择用于筛选的基准时间"
        )

    # 合并日期和时间
    filter_datetime = dt.combine(filter_date, filter_time)

    # 分类任务
    due_tasks = []  # 到期的任务
    future_tasks = []  # 未来的任务
    no_time_tasks = []  # 没有时间信息的任务

    for item in plan_items:
        task_datetime = parse_task_datetime(item)

        if task_datetime is None:
            no_time_tasks.append(item)
        elif task_datetime <= filter_datetime:
            due_tasks.append(item)
        else:
            future_tasks.append(item)

    # 显示统计信息
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("🔴 到期任务", len(due_tasks))
    with col2:
        st.metric("🟡 未来任务", len(future_tasks))
    with col3:
        st.metric("⚪ 无时间信息", len(no_time_tasks))

    # 显示到期任务
    if due_tasks:
        st.subheader("🔴 到期任务 (需要立即处理)")
        st.warning(f"⚠️ 有 {len(due_tasks)} 个任务已到部署时间，需要立即处理！")
        display_task_table(due_tasks, "due")

    # 显示未来任务
    if future_tasks:
        st.subheader("🟡 未来任务")
        st.info(f"ℹ️ 有 {len(future_tasks)} 个任务计划在未来执行")

        # 可折叠显示未来任务
        with st.expander("查看未来任务详情", expanded=False):
            display_task_table(future_tasks, "future")

    # 显示无时间信息的任务
    if no_time_tasks:
        st.subheader("⚪ 无时间信息任务")
        st.info(f"ℹ️ 有 {len(no_time_tasks)} 个任务没有明确的时间信息")

        # 可折叠显示无时间信息任务
        with st.expander("查看无时间信息任务", expanded=False):
            display_task_table(no_time_tasks, "no_time")

def parse_deployment_plan_table(deployment_plan_text):
    """解析发布计划表格文本"""
    lines = deployment_plan_text.strip().split('\n')
    plan_items = []

    # 找到表格开始位置
    table_start = -1
    for i, line in enumerate(lines):
        if '| 计划部署日期 |' in line:
            table_start = i + 2  # 跳过表头和分隔线
            break

    if table_start == -1:
        return []

    # 解析表格行
    for line in lines[table_start:]:
        line = line.strip()
        if not line or not line.startswith('|'):
            continue

        # 分割表格列
        columns = [col.strip() for col in line.split('|')[1:-1]]  # 去掉首尾空元素

        if len(columns) >= 6:
            plan_items.append({
                '计划部署日期': columns[0],
                '时间窗口': columns[1],
                '客户名': columns[2],
                '租户名': columns[3],
                'Service名': columns[4],
                '是否部署PS代码': columns[5]
            })

    return plan_items

def parse_task_datetime(item):
    """解析任务的日期时间"""
    try:
        from datetime import datetime as dt

        date_str = item.get('计划部署日期', '').strip()
        time_str = item.get('时间窗口', '').strip()

        if not date_str or not time_str:
            return None

        # 解析日期 (格式: 2025-06-22)
        try:
            date_obj = dt.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return None

        # 解析时间窗口 (格式: 17:00-19:00)
        if '-' in time_str:
            start_time_str = time_str.split('-')[0].strip()
        else:
            start_time_str = time_str

        try:
            time_obj = dt.strptime(start_time_str, '%H:%M').time()
        except ValueError:
            return None

        # 合并日期和时间
        return dt.combine(date_obj, time_obj)

    except Exception:
        return None

def display_task_table(tasks, task_type):
    """显示任务表格"""
    if not tasks:
        return

    # 创建DataFrame用于显示
    import pandas as pd

    df_data = []
    for task in tasks:
        df_data.append({
            '计划部署日期': task.get('计划部署日期', ''),
            '时间窗口': task.get('时间窗口', ''),
            '客户名': task.get('客户名', ''),
            '租户名': task.get('租户名', ''),
            'Service名': task.get('Service名', ''),
            '是否部署PS代码': task.get('是否部署PS代码', '')
        })

    df = pd.DataFrame(df_data)

    # 根据任务类型设置样式
    if task_type == "due":
        st.error("⚠️ 以下任务已到部署时间，请立即处理：")
    elif task_type == "future":
        st.info("📅 以下任务计划在未来执行：")
    else:
        st.info("📋 以下任务没有明确的时间信息：")

    # 显示表格
    st.dataframe(
        df,
        use_container_width=True,
        hide_index=True
    )

def approve_plan():
    """审批发布计划"""
    try:
        result = st.session_state.agent.approve_deployment_plan()
        if result["success"]:
            st.success(result["message"])
            st.session_state.deployment_plan.update(result["data"])
            st.rerun()
        else:
            st.error(result["message"])
    except Exception as e:
        st.error(f"审批失败: {str(e)}")

def reject_plan():
    """拒绝发布计划"""
    reason = st.text_input("拒绝原因", placeholder="请输入拒绝原因...")
    if st.button("确认拒绝"):
        try:
            result = st.session_state.agent.reject_deployment_plan(reason)
            if result["success"]:
                st.success(result["message"])
                st.session_state.deployment_plan.update(result["data"])
                st.rerun()
            else:
                st.error(result["message"])
        except Exception as e:
            st.error(f"拒绝失败: {str(e)}")

def display_jenkins_jobs_selection():
    """显示Jenkins jobs选择"""
    st.subheader("🔧 选择要执行的Jenkins Jobs")
    
    if not st.session_state.agent.current_state or not st.session_state.agent.current_state.jenkins_jobs:
        st.error("没有可用的Jenkins jobs")
        return
    
    jobs = st.session_state.agent.current_state.jenkins_jobs
    
    # 创建选择框
    selected_indices = []
    for i, job in enumerate(jobs):
        selected = st.checkbox(
            f"{job['job_name']} - {job['customer_name']} ({job['service_name']})",
            value=True,
            key=f"job_{i}"
        )
        if selected:
            selected_indices.append(i)
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🚀 确认执行", type="primary"):
            approve_execution(selected_indices)
    
    with col2:
        if st.button("❌ 取消执行", type="secondary"):
            reject_execution()

def approve_execution(selected_indices: List[int]):
    """确认执行"""
    try:
        result = st.session_state.agent.approve_execution(selected_indices)
        if result["success"]:
            st.success(result["message"])
            st.session_state.execution_status = result["data"]
            st.rerun()
        else:
            st.error(result["message"])
    except Exception as e:
        st.error(f"执行确认失败: {str(e)}")

def reject_execution():
    """拒绝执行"""
    reason = st.text_input("取消原因", placeholder="请输入取消原因...")
    if st.button("确认取消"):
        try:
            result = st.session_state.agent.reject_execution(reason)
            if result["success"]:
                st.success(result["message"])
                st.rerun()
            else:
                st.error(result["message"])
        except Exception as e:
            st.error(f"取消失败: {str(e)}")

def execution_management_tab():
    """执行管理标签页"""
    st.header("🚀 执行管理")
    
    # 获取执行状态
    if st.button("🔄 刷新状态"):
        refresh_execution_status()
    
    if st.session_state.execution_status:
        display_execution_status()
    else:
        st.info("没有正在执行的任务")

def refresh_execution_status():
    """刷新执行状态"""
    try:
        result = st.session_state.agent.get_execution_status()
        if result["success"]:
            st.session_state.execution_status = result["data"]
        else:
            st.error(result["message"])
    except Exception as e:
        st.error(f"刷新状态失败: {str(e)}")

def display_execution_status():
    """显示执行状态"""
    data = st.session_state.execution_status

    # 状态概览
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("当前状态", data.get("workflow_state", "未知"))

    with col2:
        current_index = data.get("current_job_index", 0)
        total_jobs = len(data.get("jenkins_jobs", []))
        st.metric("执行进度", f"{current_index}/{total_jobs}")

    with col3:
        waiting = "是" if data.get("waiting_for_completion", False) else "否"
        st.metric("等待完成", waiting)

    with col4:
        completed = "是" if data.get("execution_completed", False) else "否"
        st.metric("执行完成", completed)

    # 当前执行的Job信息
    current_job = data.get("current_executing_job")
    if current_job:
        st.subheader("🔄 当前执行中的Job")
        col1, col2, col3 = st.columns(3)

        with col1:
            st.info(f"**Job名称**: {current_job.get('job_name', '未知')}")

        with col2:
            st.info(f"**客户**: {current_job.get('customer_name', '未知')}")

        with col3:
            build_number = data.get("current_build_number")
            if build_number:
                st.info(f"**Build号**: {build_number}")
            else:
                st.info("**Build号**: 获取中...")

    # Jenkins Jobs执行队列
    jenkins_jobs = data.get("jenkins_jobs", [])
    if jenkins_jobs:
        st.subheader("📋 Jenkins Jobs执行队列")

        # 创建执行状态表格
        import pandas as pd

        job_status_data = []
        current_index = data.get("current_job_index", 0)

        for i, job in enumerate(jenkins_jobs):
            # 确定状态
            if i < current_index:
                # 已完成的job，从结果中查找状态
                job_result = None
                for result in data.get("job_results", []):
                    if result.get("job_name") == job.get("job_name"):
                        job_result = result
                        break

                if job_result:
                    status = "✅ 成功" if job_result.get("status") == "SUCCESS" else "❌ 失败"
                    build_info = f"Build {job_result.get('build_number', 'N/A')}"
                else:
                    status = "❓ 未知"
                    build_info = "N/A"
            elif i == current_index and data.get("waiting_for_completion"):
                status = "🔄 执行中"
                build_number = data.get("current_build_number")
                build_info = f"Build {build_number}" if build_number else "启动中..."
            elif i == current_index:
                status = "⏳ 准备中"
                build_info = "等待启动"
            else:
                status = "⏸️ 等待"
                build_info = "排队中"

            job_status_data.append({
                "序号": i + 1,
                "状态": status,
                "Job名称": job.get("job_name", ""),
                "客户名": job.get("customer_name", ""),
                "服务名": job.get("service_name", ""),
                "部署顺序": job.get("deployment_order", ""),
                "Build信息": build_info
            })

        df = pd.DataFrame(job_status_data)
        st.dataframe(df, use_container_width=True, hide_index=True)

    # 执行结果详情
    if data.get("job_results"):
        st.subheader("📊 执行结果详情")
        for i, result in enumerate(data["job_results"]):
            job_name = result.get('job_name', '未知')
            status = result.get('status', '未知')

            # 根据状态选择图标和颜色
            if status == "SUCCESS":
                icon = "✅"
                color = "success"
            elif status in ["FAILURE", "FAILED"]:
                icon = "❌"
                color = "error"
            else:
                icon = "❓"
                color = "info"

            with st.expander(f"{icon} Job {i+1}: {job_name} - {status}"):
                col1, col2 = st.columns(2)

                with col1:
                    st.write("**基本信息**:")
                    st.write(f"- Job名称: {job_name}")
                    st.write(f"- 状态: {status}")
                    st.write(f"- Build号: {result.get('build_number', 'N/A')}")

                    duration = result.get('duration', 0)
                    if duration > 0:
                        duration_sec = duration / 1000
                        st.write(f"- 持续时间: {duration_sec:.1f}秒")

                with col2:
                    st.write("**链接信息**:")
                    url = result.get('url', '')
                    if url:
                        st.markdown(f"[🔗 查看Jenkins详情]({url})")

                    error = result.get('error', '')
                    if error:
                        st.error(f"错误信息: {error}")

    # 消息和错误
    col1, col2 = st.columns(2)

    with col1:
        if data.get("messages"):
            st.subheader("📝 执行消息")
            # 只显示最新的10条消息
            recent_messages = data["messages"][-10:]
            for msg in recent_messages:
                st.info(msg)

    with col2:
        if data.get("errors"):
            st.subheader("❌ 错误信息")
            for error in data["errors"]:
                st.error(error)

def monitoring_dashboard_tab():
    """监控面板标签页"""
    st.header("📊 Jenkins Job 实时监控")

    # 初始化session state
    if 'monitoring_job' not in st.session_state:
        st.session_state.monitoring_job = None
    if 'auto_refresh' not in st.session_state:
        st.session_state.auto_refresh = False
    if 'last_refresh_time' not in st.session_state:
        st.session_state.last_refresh_time = 0
    if 'refresh_counter' not in st.session_state:
        st.session_state.refresh_counter = 0

    # Job输入区域
    st.subheader("🔍 Job配置")
    col1, col2, col3 = st.columns([3, 2, 2])

    with col1:
        job_name = st.text_input("Job名称", placeholder="输入Jenkins job名称", key="monitor_job_name")

    with col2:
        build_number = st.number_input("Build号", min_value=1, value=1, key="monitor_build_number")

    with col3:
        st.write("") # 空行对齐
        auto_refresh = st.checkbox("🔄 自动刷新 (5秒)", value=st.session_state.auto_refresh)
        st.session_state.auto_refresh = auto_refresh

    # 控制按钮
    col1, col2, col3 = st.columns([2, 2, 2])

    with col1:
        if st.button("🚀 开始监控", type="primary"):
            if job_name:
                start_monitoring_job(job_name, build_number)
            else:
                st.error("请输入Job名称")

    with col2:
        if st.button("🔄 手动刷新"):
            if st.session_state.monitoring_job:
                refresh_monitoring_data()
            else:
                st.warning("请先开始监控一个Job")

    with col3:
        if st.button("⏹️ 停止监控"):
            stop_monitoring()

    # 显示监控结果
    if st.session_state.monitoring_job:
        display_realtime_monitoring()

        # 自动刷新逻辑
        if st.session_state.auto_refresh:
            st.info(f"🔄 自动刷新已启用 (已刷新{st.session_state.refresh_counter}次)")

            # 使用占位符显示倒计时
            countdown_placeholder = st.empty()

            import time
            for i in range(5, 0, -1):
                countdown_placeholder.info(f"⏰ {i}秒后自动刷新...")
                time.sleep(1)

            # 刷新数据
            countdown_placeholder.success("🔄 正在刷新数据...")
            refresh_monitoring_data()
            st.session_state.refresh_counter += 1
            st.rerun()
    else:
        st.info("💡 请输入Job名称和Build号，然后点击'开始监控'来监控Jenkins job的执行状态")

def start_monitoring_job(job_name: str, build_number: int):
    """开始监控Jenkins job"""
    try:
        # 保存监控的job信息
        st.session_state.monitoring_job = {
            "job_name": job_name,
            "build_number": build_number,
            "status": None,
            "console_output": "",
            "stages": None,
            "last_update": None
        }

        # 重置刷新相关的状态
        st.session_state.last_refresh_time = 0
        st.session_state.refresh_counter = 0

        # 立即获取一次数据
        refresh_monitoring_data()
        st.success(f"✅ 开始监控 {job_name} Build {build_number}")

    except Exception as e:
        st.error(f"❌ 开始监控失败: {str(e)}")

def refresh_monitoring_data():
    """刷新监控数据"""
    if not st.session_state.monitoring_job:
        return

    job_name = st.session_state.monitoring_job["job_name"]
    build_number = st.session_state.monitoring_job["build_number"]

    try:
        # 获取job状态
        status_result = st.session_state.agent.jenkins_client.get_build_status(job_name, build_number)

        # 获取控制台输出
        console_result = st.session_state.agent.jenkins_client.get_console_output(job_name, build_number)

        # 获取pipeline stages信息
        stages_result = st.session_state.agent.jenkins_client.get_pipeline_stages(job_name, build_number)

        if status_result["success"]:
            st.session_state.monitoring_job["status"] = status_result

        if console_result["success"]:
            # 只保留最新的20行日志
            console_output = console_result["console_output"]
            if console_output:
                lines = console_output.strip().split('\n')
                last_20_lines = lines[-20:] if len(lines) > 20 else lines
                st.session_state.monitoring_job["console_output"] = '\n'.join(last_20_lines)
            else:
                st.session_state.monitoring_job["console_output"] = "暂无日志输出"

        if stages_result["success"]:
            st.session_state.monitoring_job["stages"] = stages_result

        # 更新时间戳
        import time
        st.session_state.monitoring_job["last_update"] = time.time()

    except Exception as e:
        st.error(f"❌ 刷新数据失败: {str(e)}")

def stop_monitoring():
    """停止监控"""
    st.session_state.monitoring_job = None
    st.session_state.auto_refresh = False
    st.session_state.last_refresh_time = 0
    st.session_state.refresh_counter = 0
    st.success("⏹️ 已停止监控")

def display_realtime_monitoring():
    """显示实时监控结果"""
    job_info = st.session_state.monitoring_job

    if not job_info:
        return

    st.divider()
    st.subheader(f"📊 {job_info['job_name']} Build {job_info['build_number']}")

    # 显示最后更新时间
    if job_info.get("last_update"):
        import datetime
        last_update = datetime.datetime.fromtimestamp(job_info["last_update"])
        st.caption(f"最后更新: {last_update.strftime('%H:%M:%S')}")

    # 使用标签页显示不同信息
    tab1, tab2 = st.tabs(["📊 整体构建状态", "🔄 当前Stage"])

    with tab1:
        # 整体构建状态信息
        if job_info.get("status"):
            status_info = job_info["status"]

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                status = status_info.get("status", "未知")
                if status == "SUCCESS":
                    st.success(f"✅ {status}")
                elif status == "FAILURE":
                    st.error(f"❌ {status}")
                elif status == "RUNNING":
                    st.info(f"🔄 {status}")
                else:
                    st.warning(f"⚠️ {status}")

            with col2:
                building = status_info.get("building", False)
                if building:
                    st.info("🔄 构建中")
                else:
                    st.success("✅ 已完成")

            with col3:
                duration = status_info.get("duration", 0)
                if duration > 0:
                    duration_sec = duration / 1000
                    st.metric("持续时间", f"{duration_sec:.1f}秒")
                else:
                    st.metric("持续时间", "进行中...")

            with col4:
                url = status_info.get("url", "")
                if url:
                    st.markdown(f"[🔗 查看详情]({url})")

    with tab2:
        # 当前Stage信息
        if job_info.get("stages"):
            stages_info = job_info["stages"]

            if stages_info.get("success"):
                current_stage = stages_info.get("current_stage")
                all_stages = stages_info.get("stages", [])
                total_stages = stages_info.get("total_stages", 0)

                # 显示当前stage
                if current_stage:
                    st.subheader(f"🎯 当前Stage: {current_stage['name']}")

                    col1, col2, col3 = st.columns(3)

                    with col1:
                        stage_status = current_stage.get("status", "未知")
                        if stage_status == "SUCCESS":
                            st.success(f"✅ {stage_status}")
                        elif stage_status == "FAILURE":
                            st.error(f"❌ {stage_status}")
                        elif stage_status == "IN_PROGRESS":
                            st.info(f"🔄 {stage_status}")
                        else:
                            st.warning(f"⚠️ {stage_status}")

                    with col2:
                        stage_duration = current_stage.get("duration", 0)
                        if stage_duration > 0:
                            duration_sec = stage_duration / 1000
                            st.metric("Stage持续时间", f"{duration_sec:.1f}秒")
                        else:
                            st.metric("Stage持续时间", "进行中...")

                    with col3:
                        st.metric("总Stage数", f"{total_stages}")

                # 显示所有stages的进度
                if all_stages:
                    st.subheader("📋 所有Stages")

                    for i, stage in enumerate(all_stages):
                        stage_name = stage.get("name", f"Stage {i+1}")
                        stage_status = stage.get("status", "未知")

                        # 使用不同的图标表示不同状态
                        if stage_status == "SUCCESS":
                            icon = "✅"
                            color = "normal"
                        elif stage_status == "FAILURE":
                            icon = "❌"
                            color = "normal"
                        elif stage_status == "IN_PROGRESS":
                            icon = "🔄"
                            color = "normal"
                        else:
                            icon = "⏳"
                            color = "normal"

                        st.write(f"{icon} **{stage_name}** - {stage_status}")

                # 显示解析来源
                if stages_info.get("parsed_from_console"):
                    st.info("ℹ️ Stage信息从控制台日志解析获得")
            else:
                st.warning("⚠️ 无法获取Stage信息")
                st.info("可能原因：该Job不是Pipeline类型，或者Jenkins版本不支持Workflow API")
        else:
            st.info("🔄 正在获取Stage信息...")

    # 控制台输出 (最新20行)
    st.subheader("📝 最新日志 (最近20行)")

    console_output = job_info.get("console_output", "")
    if console_output:
        # 使用代码块显示日志，支持滚动
        st.code(console_output, language="text", line_numbers=True)
    else:
        st.info("暂无日志输出")

    # 自动刷新状态指示
    if st.session_state.auto_refresh:
        st.info("🔄 自动刷新已启用 (每5秒刷新一次)")
    else:
        st.info("⏸️ 自动刷新已暂停")

def advanced_llm_config_page():
    """高级LLM配置页面"""
    # 页面标题和返回按钮
    col1, col2 = st.columns([6, 1])
    with col1:
        st.header("🤖 LLM高级配置管理")
    with col2:
        if st.button("← 返回", key="back_to_main"):
            st.session_state.show_advanced_llm_config = False
            st.rerun()

    # 当前配置显示
    current_config = llm_config_manager.get_current_config()

    col1, col2 = st.columns(2)
    with col1:
        st.info(f"**当前供应商**: {current_config['provider_name']}")
    with col2:
        st.info(f"**当前模型**: {current_config['model']}")

    st.divider()

    # 配置选择区域
    st.subheader("🔧 配置选择")

    # 获取可用供应商
    providers = llm_config_manager.get_available_providers()

    col1, col2 = st.columns(2)

    with col1:
        st.write("**选择LLM供应商**")

        # 显示供应商状态
        for provider in providers:
            status_icon = "✅" if provider["configured"] else "❌"
            st.write(f"{status_icon} {provider['label']} - {provider['status']}")

        # 供应商选择
        provider_options = [p["value"] for p in providers if p["configured"]]
        if not provider_options:
            st.error("❌ 没有配置任何LLM供应商的API密钥")
            st.info("请在.env文件中配置至少一个供应商的API密钥")
            return

        selected_provider = st.selectbox(
            "选择供应商",
            options=provider_options,
            format_func=lambda x: next(p["label"] for p in providers if p["value"] == x),
            index=provider_options.index(current_config["provider"]) if current_config["provider"] in provider_options else 0,
            key="advanced_provider"
        )

    with col2:
        st.write("**选择模型**")

        # 添加刷新按钮
        col2_1, col2_2 = st.columns([3, 1])
        with col2_2:
            if st.button("🔄 刷新模型", help="从供应商实时获取最新模型列表", key="advanced_refresh"):
                # 清除缓存并重新获取
                provider_enum = LLMProvider(selected_provider)
                llm_config_manager.clear_model_cache(provider_enum)
                st.rerun()

        # 获取选中供应商的模型
        try:
            provider_enum = LLMProvider(selected_provider)

            # 显示加载状态
            with st.spinner(f"正在获取{llm_config_manager.get_provider_display_name(provider_enum)}的模型列表..."):
                models = llm_config_manager.get_available_models(provider_enum)

            if models:
                # 显示模型数量和获取时间
                st.info(f"✅ 成功获取 {len(models)} 个模型")

                # 显示模型信息
                for model in models:
                    with st.expander(f"📋 {model['label']}", expanded=False):
                        st.write(f"**模型ID**: `{model['value']}`")
                        st.write(f"**最大Token数**: {model['max_tokens']:,}")
                        st.write(f"**每1K Token成本**: ${model['cost_per_1k']:.6f}")
                        st.write(f"**支持流式输出**: {'是' if model['streaming'] else '否'}")

                        # 显示额外信息（如果有）
                        if 'description' in model and model['description']:
                            st.write(f"**描述**: {model['description']}")
                        if 'owned_by' in model:
                            st.write(f"**提供方**: {model['owned_by']}")
                        if 'created' in model:
                            import datetime
                            created_time = datetime.datetime.fromtimestamp(model['created'])
                            st.write(f"**创建时间**: {created_time.strftime('%Y-%m-%d %H:%M:%S')}")

                # 模型选择
                model_options = [m["value"] for m in models]

                # 确定默认选择的模型
                default_index = 0
                if current_config["model"] in model_options:
                    default_index = model_options.index(current_config["model"])

                selected_model = st.selectbox(
                    "选择模型",
                    options=model_options,
                    format_func=lambda x: next(m["label"] for m in models if m["value"] == x),
                    index=default_index,
                    help="选择要使用的具体模型",
                    key="advanced_model"
                )

                # 显示选中模型的详细信息
                selected_model_info = next(m for m in models if m["value"] == selected_model)
                st.success(f"已选择: {selected_model_info['label']} (最大{selected_model_info['max_tokens']:,} tokens)")

            else:
                st.error("❌ 该供应商没有可用的模型")
                st.info("可能的原因：\n- API密钥无效\n- 网络连接问题\n- 供应商服务暂时不可用")
                return

        except Exception as e:
            st.error(f"❌ 获取模型列表失败: {e}")

            # 显示错误详情
            with st.expander("🔍 错误详情", expanded=False):
                st.code(str(e))

            # 提供解决建议
            st.info("💡 解决建议：\n1. 检查API密钥是否正确\n2. 确认网络连接正常\n3. 点击'🔄 刷新模型'重试")
            return

    st.divider()

    # 配置操作区域
    st.subheader("⚙️ 配置操作")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("🔄 应用配置", type="primary", key="advanced_apply"):
            try:
                llm_config_manager.set_current_config(selected_provider, selected_model)
                st.success(f"✅ 配置已更新: {selected_provider} - {selected_model}")
                st.rerun()
            except Exception as e:
                st.error(f"❌ 配置更新失败: {e}")

    with col2:
        if st.button("🧪 测试连接", key="advanced_test"):
            with st.spinner("正在测试连接..."):
                test_result = llm_config_manager.test_connection(selected_provider, selected_model)

                if test_result["success"]:
                    st.success(f"✅ {test_result['message']}")
                    with st.expander("📝 测试响应"):
                        st.write(test_result["response"])
                else:
                    st.error(f"❌ {test_result['message']}")
                    with st.expander("🔍 错误详情"):
                        st.code(test_result["error"])

    with col3:
        if st.button("📋 查看配置", key="advanced_view"):
            st.json({
                "当前供应商": current_config["provider"],
                "当前模型": current_config["model"],
                "选中供应商": selected_provider,
                "选中模型": selected_model
            })

    with col4:
        if st.button("🔄 刷新全部", help="清除所有供应商的模型缓存", key="advanced_refresh_all"):
            with st.spinner("正在刷新所有供应商的模型列表..."):
                llm_config_manager.clear_model_cache()
                st.success("✅ 已清除所有模型缓存")
                st.rerun()

    st.divider()

    # API密钥配置说明
    st.subheader("🔑 API密钥配置说明")

    with st.expander("📖 如何配置API密钥", expanded=False):
        st.markdown("""
        ### 配置步骤：

        1. **复制环境配置文件**
           ```bash
           cp .env.example .env
           ```

        2. **编辑.env文件，添加对应的API密钥**

           **OpenAI配置：**
           ```
           OPENAI_API_KEY=sk-your-openai-api-key
           OPENAI_BASE_URL=https://api.openai.com/v1
           ```

           **Google Gemini配置：**
           ```
           GOOGLE_API_KEY=your-google-api-key
           ```

           **Anthropic Claude配置：**
           ```
           ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key
           ```

        3. **重启应用**
           ```bash
           python run.py
           ```

        ### 获取API密钥：

        - **OpenAI**: https://platform.openai.com/api-keys
        - **Google Gemini**: https://makersuite.google.com/app/apikey
        - **Anthropic Claude**: https://console.anthropic.com/

        ### 注意事项：

        - API密钥请妥善保管，不要泄露给他人
        - 不同供应商的计费方式可能不同，请注意使用成本
        - 建议在测试环境中先验证配置是否正确
        """)

    # 模型管理
    with st.expander("📊 模型列表管理", expanded=False):
        st.markdown("### 实时模型信息")

        # 显示所有供应商的模型统计
        col1, col2, col3 = st.columns(3)

        for i, provider in enumerate([LLMProvider.OPENAI, LLMProvider.GOOGLE, LLMProvider.ANTHROPIC]):
            with [col1, col2, col3][i]:
                st.write(f"**{llm_config_manager.get_provider_display_name(provider)}**")

                api_key = llm_config_manager.get_api_key(provider)
                if api_key:
                    try:
                        models = llm_config_manager.get_available_models(provider)
                        st.metric(
                            label="可用模型",
                            value=len(models),
                            help=f"从{provider.value}实时获取的模型数量"
                        )

                        if models:
                            # 显示最新模型
                            latest_model = models[0]
                            st.write(f"🆕 最新: {latest_model['label']}")

                            # 显示成本范围
                            costs = [m['cost_per_1k'] for m in models]
                            if costs:
                                min_cost = min(costs)
                                max_cost = max(costs)
                                st.write(f"💰 成本: ${min_cost:.6f} - ${max_cost:.6f}/1K")

                        if st.button(f"🔄 刷新{provider.value}", key=f"advanced_refresh_{provider.value}"):
                            llm_config_manager.clear_model_cache(provider)
                            st.rerun()

                    except Exception as e:
                        st.error(f"❌ 获取失败")
                        st.caption(str(e)[:50] + "...")
                else:
                    st.warning("⚠️ 未配置API密钥")

        st.divider()

        # 缓存管理
        st.markdown("### 缓存管理")
        col1, col2 = st.columns(2)

        with col1:
            st.write("**缓存状态**")
            cache_info = []
            for provider in LLMProvider:
                cache_key = provider.value
                if cache_key in llm_config_manager._model_cache:
                    import time
                    cache_time = llm_config_manager._cache_timestamp.get(cache_key, 0)
                    age = int(time.time() - cache_time)
                    cache_info.append(f"✅ {provider.value}: {age}秒前")
                else:
                    cache_info.append(f"❌ {provider.value}: 无缓存")

            for info in cache_info:
                st.write(info)

        with col2:
            st.write("**缓存操作**")
            if st.button("🗑️ 清除所有缓存", key="advanced_clear_cache"):
                llm_config_manager.clear_model_cache()
                st.success("已清除所有缓存")
                st.rerun()

            st.caption(f"缓存有效期: {llm_config_manager.CACHE_DURATION}秒")

    # 高级配置
    with st.expander("⚙️ 高级配置", expanded=False):
        st.markdown("### 模型参数调整")

        col1, col2 = st.columns(2)
        with col1:
            temperature = st.slider(
                "Temperature (创造性)",
                min_value=0.0,
                max_value=2.0,
                value=0.7,
                step=0.1,
                help="控制输出的随机性，值越高越有创造性",
                key="advanced_temperature"
            )

        with col2:
            max_tokens = st.number_input(
                "最大Token数",
                min_value=1,
                max_value=4096,
                value=1000,
                help="限制模型输出的最大长度",
                key="advanced_max_tokens"
            )

        if st.button("🧪 使用自定义参数测试", key="advanced_custom_test"):
            with st.spinner("正在测试自定义参数..."):
                try:
                    # 临时创建LLM实例进行测试
                    original_provider = llm_config_manager.current_provider
                    original_model = llm_config_manager.current_model

                    llm_config_manager.set_current_config(selected_provider, selected_model)
                    llm = llm_config_manager.create_llm_instance(temperature=temperature, max_tokens=max_tokens)

                    from langchain_core.messages import HumanMessage
                    test_message = HumanMessage(content="请简单介绍一下你自己")
                    response = llm.invoke([test_message])

                    # 恢复原配置
                    llm_config_manager.current_provider = original_provider
                    llm_config_manager.current_model = original_model

                    st.success("✅ 自定义参数测试成功")
                    with st.expander("📝 测试响应"):
                        st.write(response.content if hasattr(response, 'content') else str(response))

                except Exception as e:
                    st.error(f"❌ 自定义参数测试失败: {e}")

def llm_config_tab():
    """LLM配置标签页（已废弃，保留用于兼容性）"""
    st.header("🤖 LLM配置管理")

    # 显示迁移提示
    st.info("💡 **LLM配置已移至左侧菜单栏**")
    st.markdown("""
    ### 🔄 新的配置方式：

    1. **基础配置**: 在左侧菜单栏的 "🤖 LLM配置" 部分进行快速配置
    2. **高级配置**: 点击左侧的 "⚙️ 高级配置" 按钮访问详细配置选项

    ### ✨ 新功能优势：
    - 🚀 更便捷的访问方式
    - 🎯 简化的基础配置流程
    - ⚙️ 完整的高级配置选项
    - 📱 更好的界面布局
    """)

    # 当前配置显示
    current_config = llm_config_manager.get_current_config()

    st.subheader("📊 当前配置状态")
    col1, col2 = st.columns(2)
    with col1:
        st.info(f"**当前供应商**: {current_config['provider_name']}")
    with col2:
        st.info(f"**当前模型**: {current_config['model']}")

    # 快速跳转按钮
    if st.button("🚀 前往左侧菜单配置", type="primary"):
        st.balloons()
        st.success("请查看左侧菜单栏的 '🤖 LLM配置' 部分！")

if __name__ == "__main__":
    main()
