#!/usr/bin/env python3
"""
调试正则表达式问题
"""

import re

def test_regex():
    patterns = [
        r'版本[：:]?\s*(\d+R\d+\.\d+).*?环境[：:]?\s*(\w+)',  # 版本：25R1.2 环境：Prod
        r'环境[：:]?\s*(\w+).*?版本[：:]?\s*(\d+R\d+\.\d+)',  # 环境：Prod 版本：25R1.2
        r'版本[：:]?\s*(\d+R\d+\.\d+)\s+环境[：:]?\s*(\w+)',  # 版本：25R1.5 环境：Staging
        r'(\d+R\d+\.\d+)\s+(\w+)',  # 25R1.2 Prod (放在最后，避免误匹配)
    ]

    text = '版本：25R1.5 环境：Staging'
    print(f"测试文本: '{text}'")
    print()
    
    for i, pattern in enumerate(patterns):
        print(f"模式{i+1}: {pattern}")
        match = re.search(pattern, text)
        if match:
            groups = match.groups()
            print(f"  匹配成功: {groups}")
            if len(groups) == 2:
                if 'R' in groups[0]:
                    print(f"  解析结果: 版本={groups[0]}, 环境={groups[1]}")
                else:
                    print(f"  解析结果: 版本={groups[1]}, 环境={groups[0]}")
            break
        else:
            print("  不匹配")
        print()

if __name__ == "__main__":
    test_regex()
