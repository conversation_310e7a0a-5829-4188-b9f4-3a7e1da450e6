#!/usr/bin/env python3
"""
自动刷新功能测试脚本
测试Streamlit自动刷新机制
"""

import streamlit as st
import time
from datetime import datetime

# 页面配置
st.set_page_config(
    page_title="自动刷新测试",
    page_icon="🔄",
    layout="wide"
)

def main():
    """主函数"""
    st.title("🔄 自动刷新功能测试")
    
    # 初始化session state
    if 'auto_refresh' not in st.session_state:
        st.session_state.auto_refresh = False
    if 'last_refresh_time' not in st.session_state:
        st.session_state.last_refresh_time = 0
    if 'refresh_counter' not in st.session_state:
        st.session_state.refresh_counter = 0
    if 'start_time' not in st.session_state:
        st.session_state.start_time = time.time()
    
    # 控制面板
    st.subheader("🎛️ 控制面板")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        auto_refresh = st.checkbox("🔄 启用自动刷新 (5秒)", value=st.session_state.auto_refresh)
        st.session_state.auto_refresh = auto_refresh
    
    with col2:
        if st.button("🔄 手动刷新"):
            st.session_state.refresh_counter += 1
            st.session_state.last_refresh_time = time.time()
            st.success("手动刷新完成")
    
    with col3:
        if st.button("🔄 重置计数器"):
            st.session_state.refresh_counter = 0
            st.session_state.last_refresh_time = 0
            st.session_state.start_time = time.time()
            st.success("计数器已重置")
    
    st.divider()
    
    # 显示状态信息
    st.subheader("📊 状态信息")
    
    current_time = time.time()
    elapsed_time = current_time - st.session_state.start_time
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("当前时间", datetime.now().strftime("%H:%M:%S"))
    
    with col2:
        st.metric("运行时间", f"{int(elapsed_time)}秒")
    
    with col3:
        st.metric("刷新次数", st.session_state.refresh_counter)
    
    with col4:
        if st.session_state.last_refresh_time > 0:
            last_refresh = datetime.fromtimestamp(st.session_state.last_refresh_time)
            st.metric("最后刷新", last_refresh.strftime("%H:%M:%S"))
        else:
            st.metric("最后刷新", "未刷新")
    
    # 自动刷新逻辑
    if st.session_state.auto_refresh:
        # 初始化最后刷新时间
        if st.session_state.last_refresh_time == 0:
            st.session_state.last_refresh_time = current_time
        
        # 检查是否需要刷新
        time_since_last_refresh = current_time - st.session_state.last_refresh_time
        
        if time_since_last_refresh >= 5:  # 5秒刷新一次
            st.session_state.last_refresh_time = current_time
            st.session_state.refresh_counter += 1
        
        # 显示倒计时
        remaining_time = max(0, 5 - int(time_since_last_refresh))
        
        if remaining_time > 0:
            st.info(f"🔄 自动刷新启用 - 下次刷新: {remaining_time}秒")
        else:
            st.info("🔄 正在刷新...")
        
        # 显示进度条
        progress = min(1.0, time_since_last_refresh / 5.0)
        st.progress(progress)
        
        # 触发刷新
        time.sleep(1)
        st.rerun()
    else:
        st.info("⏸️ 自动刷新已禁用")
    
    # 测试数据显示
    st.subheader("📝 测试数据")
    
    # 模拟动态数据
    test_data = []
    for i in range(10):
        timestamp = datetime.now().strftime("%H:%M:%S")
        value = (int(current_time) + i) % 100
        test_data.append(f"[{timestamp}] 测试数据 {i+1}: {value}")
    
    st.code("\n".join(test_data), language="text")
    
    # 说明信息
    with st.expander("📖 使用说明", expanded=False):
        st.markdown("""
        ### 🎯 测试目的
        验证Streamlit自动刷新功能是否正常工作
        
        ### 🔧 测试方法
        1. 启用"自动刷新"复选框
        2. 观察页面是否每5秒自动更新
        3. 检查刷新次数是否递增
        4. 观察倒计时是否正常显示
        
        ### ✅ 预期结果
        - 页面每5秒自动刷新一次
        - 刷新次数持续递增
        - 倒计时从4秒倒数到0秒
        - 测试数据实时更新
        
        ### 🐛 问题排查
        如果自动刷新不工作：
        1. 检查浏览器控制台是否有错误
        2. 确认Streamlit版本是否支持st.rerun()
        3. 检查网络连接是否稳定
        """)

if __name__ == "__main__":
    main()
