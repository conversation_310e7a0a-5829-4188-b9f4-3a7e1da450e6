#!/usr/bin/env python3
"""
LLM配置侧边栏演示脚本
展示将LLM配置移至左侧菜单的效果
"""

import streamlit as st
import os
import sys

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 页面配置
st.set_page_config(
    page_title="LLM配置侧边栏演示",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

def mock_llm_config():
    """模拟LLM配置管理器"""
    return {
        'provider_name': 'OpenAI',
        'model': 'gpt-3.5-turbo',
        'provider': 'openai'
    }

def mock_providers():
    """模拟可用供应商"""
    return [
        {"value": "openai", "label": "OpenAI", "configured": True},
        {"value": "google", "label": "Google Gemini", "configured": False},
        {"value": "anthropic", "label": "Anthropic Claude", "configured": False}
    ]

def mock_models():
    """模拟可用模型"""
    return [
        {"value": "gpt-3.5-turbo", "label": "GPT-3.5 Turbo"},
        {"value": "gpt-4", "label": "GPT-4"},
        {"value": "gpt-4-turbo", "label": "GPT-4 Turbo"}
    ]

def llm_config_sidebar_demo():
    """演示版本的侧边栏LLM配置面板"""
    # 当前配置显示
    current_config = mock_llm_config()
    
    # 显示当前配置状态
    st.write(f"**当前供应商**: {current_config['provider_name']}")
    st.write(f"**当前模型**: {current_config['model']}")
    
    # 获取可用供应商
    providers = mock_providers()
    provider_options = [p["value"] for p in providers if p["configured"]]
    
    if not provider_options:
        st.error("❌ 未配置API密钥")
        st.info("请在.env文件中配置API密钥")
        return
    
    # 供应商选择
    selected_provider = st.selectbox(
        "选择供应商",
        options=provider_options,
        format_func=lambda x: next(p["label"] for p in providers if p["value"] == x),
        index=0,
        key="demo_sidebar_provider"
    )
    
    # 模型选择
    models = mock_models()
    model_options = [m["value"] for m in models]
    
    selected_model = st.selectbox(
        "选择模型",
        options=model_options,
        format_func=lambda x: next(m["label"] for m in models if m["value"] == x),
        index=0,
        key="demo_sidebar_model"
    )
    
    # 应用配置按钮
    if st.button("🔄 应用配置", type="primary", key="demo_sidebar_apply"):
        st.success("✅ 配置已更新")
        st.balloons()
    
    # 测试连接按钮
    if st.button("🧪 测试连接", key="demo_sidebar_test"):
        with st.spinner("测试中..."):
            st.success("✅ 连接成功")

def advanced_llm_config_demo():
    """演示版本的高级LLM配置页面"""
    # 页面标题和返回按钮
    col1, col2 = st.columns([6, 1])
    with col1:
        st.header("🤖 LLM高级配置管理")
    with col2:
        if st.button("← 返回", key="demo_back_to_main"):
            st.session_state.show_demo_advanced_config = False
            st.rerun()

    # 当前配置显示
    current_config = mock_llm_config()

    col1, col2 = st.columns(2)
    with col1:
        st.info(f"**当前供应商**: {current_config['provider_name']}")
    with col2:
        st.info(f"**当前模型**: {current_config['model']}")

    st.divider()

    # 配置选择区域
    st.subheader("🔧 配置选择")
    
    st.markdown("""
    ### 📋 功能特性：
    - 🔧 实时切换LLM供应商和模型
    - 🔄 实时获取最新模型列表
    - 🧪 连接测试功能
    - ⚙️ 高级参数调整（Temperature、Max Tokens）
    - 📊 模型信息展示（成本、Token限制等）
    - 💾 智能缓存机制
    """)
    
    # 模拟配置操作
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🔄 应用配置", type="primary", key="demo_advanced_apply"):
            st.success("✅ 配置已更新")
    
    with col2:
        if st.button("🧪 测试连接", key="demo_advanced_test"):
            st.success("✅ 连接测试成功")
    
    with col3:
        if st.button("📋 查看配置", key="demo_advanced_view"):
            st.json(current_config)
    
    with col4:
        if st.button("🔄 刷新全部", key="demo_advanced_refresh"):
            st.success("✅ 已刷新所有模型缓存")

def environment_config_demo():
    """演示环境配置检查功能"""
    config_items = [
        {
            "name": "数据库",
            "icon": "🗄️",
            "configured": True,
            "details": {
                "MYSQL_HOST": "demo-database-host.amazonaws.com",
                "MYSQL_PORT": "3306",
                "MYSQL_USER": "admin",
                "MYSQL_PASSWORD": "********",
                "MYSQL_DATABASE": "release"
            }
        },
        {
            "name": "Jenkins",
            "icon": "🔧",
            "configured": True,
            "details": {
                "JENKINS_URL": "https://jenkins-demo.example.com",
                "JENKINS_USERNAME": "<EMAIL>",
                "JENKINS_TOKEN": "1135cc9a********"
            }
        }
    ]

    for config in config_items:
        name = config["name"]
        icon = config["icon"]
        configured = config["configured"]
        details = config["details"]

        status = "✅" if configured else "❌"

        # 创建可展开的配置详情
        with st.expander(f"{status} {icon} {name}", expanded=False):
            if configured:
                st.success(f"✅ {name}配置完整")

                # 显示配置详情
                st.subheader("📋 配置详情")

                for key, value in details.items():
                    st.write(f"- **{key}**: `{value}`")

                if name == "数据库":
                    st.markdown("**连接字符串**: `mysql://admin:***@demo-database-host.amazonaws.com:3306/release`")
                    if st.button("🔍 测试数据库连接", key=f"test_db_demo"):
                        st.success("✅ 数据库连接成功！")
                        st.info("已连接到: demo-database-host.amazonaws.com/release")

                elif name == "Jenkins":
                    st.markdown("**Jenkins地址**: [https://jenkins-demo.example.com](https://jenkins-demo.example.com)")
                    if st.button("🔍 测试Jenkins连接", key=f"test_jenkins_demo"):
                        st.success("✅ Jenkins连接成功！")
                        st.info("已连接到: https://jenkins-demo.example.com")
                        st.info("用户: <EMAIL>")
            else:
                st.error(f"❌ {name}配置不完整")

def main():
    """主函数"""
    st.title("🚀 环境配置查看功能演示")
    st.markdown("展示可展开查看数据库和Jenkins配置详情的功能")

    # 侧边栏
    with st.sidebar:
        st.header("📋 操作面板")

        # 环境配置检查
        st.subheader("🔧 环境配置")
        environment_config_demo()

        st.divider()

        # LLM配置面板
        st.subheader("🤖 LLM配置")
        llm_config_sidebar_demo()

        st.divider()

        # 快速操作
        st.subheader("⚡ 快速操作")
        if st.button("🔄 重置会话", type="secondary"):
            st.success("会话已重置")
    
    # 检查是否显示高级LLM配置
    if st.session_state.get('show_demo_advanced_config', False):
        advanced_llm_config_demo()
        return
    
    # 主要内容区域
    tab1, tab2, tab3, tab4 = st.tabs(["📝 发布计划查询", "✅ 计划审批", "🚀 执行管理", "📊 监控面板"])

    with tab1:
        st.header("📝 发布计划查询")
        st.info("这是发布计划查询页面的内容")

        # 演示新功能
        st.subheader("🎯 新增功能演示")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("""
            ### 🔧 环境配置查看功能

            **新增特性：**
            1. **可展开配置详情**: 点击左侧菜单的数据库/Jenkins配置可查看详情
            2. **配置信息展示**: 显示完整的连接参数（敏感信息脱敏）
            3. **连接测试**: 一键测试数据库和Jenkins连接状态
            4. **配置示例**: 未配置时显示配置示例

            **使用方法：**
            - 在左侧菜单栏点击"🗄️ 数据库"或"🔧 Jenkins"
            - 查看详细的配置信息
            - 点击"测试连接"验证配置
            """)

        with col2:
            st.markdown("""
            ### 🤖 LLM配置体验

            **主要改进：**
            1. **左侧菜单集成**: LLM配置现在位于左侧菜单栏，随时可访问
            2. **简化的基础配置**: 快速选择供应商和模型
            3. **高级配置入口**: 点击"⚙️ 高级配置"按钮访问详细选项
            4. **更好的用户体验**: 不需要切换标签页，配置更便捷

            **配置流程：**
            - **步骤1**: 在左侧菜单选择供应商和模型
            - **步骤2**: 点击"应用配置"保存设置
            - **步骤3**: 可选择"测试连接"验证配置
            - **步骤4**: 需要详细配置时点击"高级配置"
            """)

        st.divider()

        # 功能演示按钮
        col1, col2 = st.columns(2)

        with col1:
            if st.button("🔍 查看环境配置演示", type="secondary"):
                st.info("💡 请查看左侧菜单栏的'🔧 环境配置'部分，点击展开查看详细配置信息！")
                st.balloons()

        with col2:
            if st.button("🚀 体验高级LLM配置", type="primary"):
                st.session_state.show_demo_advanced_config = True
                st.rerun()

    with tab2:
        st.header("✅ 计划审批")
        st.info("这是计划审批页面的内容")

    with tab3:
        st.header("🚀 执行管理")
        st.info("这是执行管理页面的内容")

    with tab4:
        st.header("📊 监控面板")
        st.info("这是监控面板页面的内容")

if __name__ == "__main__":
    main()
