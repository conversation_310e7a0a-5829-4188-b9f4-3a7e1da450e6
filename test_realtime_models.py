#!/usr/bin/env python3
"""
实时模型获取功能测试脚本
测试从各个LLM供应商实时获取模型列表的功能
"""

import sys
import os
import time

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.llm_config import llm_config_manager, LLMProvider

def test_realtime_model_fetching():
    """测试实时模型获取功能"""
    print("🧪 测试实时模型获取功能")
    print("=" * 50)
    
    for provider in LLMProvider:
        print(f"\n🔍 测试 {provider.value} 供应商")
        print("-" * 30)
        
        # 检查API密钥
        api_key = llm_config_manager.get_api_key(provider)
        if not api_key:
            print(f"⚠️  {provider.value}: API密钥未配置，跳过测试")
            continue
        
        print(f"✅ {provider.value}: API密钥已配置")
        
        try:
            # 清除缓存，强制实时获取
            llm_config_manager.clear_model_cache(provider)
            
            # 记录开始时间
            start_time = time.time()
            
            # 获取模型列表
            print(f"📡 正在从 {provider.value} 实时获取模型列表...")
            models = llm_config_manager.get_available_models(provider, use_cache=False)
            
            # 记录结束时间
            end_time = time.time()
            duration = end_time - start_time
            
            if models:
                print(f"✅ 成功获取 {len(models)} 个模型 (耗时: {duration:.2f}秒)")
                
                # 显示前3个模型的详细信息
                for i, model in enumerate(models[:3]):
                    print(f"  {i+1}. {model['label']}")
                    print(f"     ID: {model['value']}")
                    print(f"     最大Token: {model['max_tokens']:,}")
                    print(f"     成本: ${model['cost_per_1k']:.6f}/1K")
                    
                    # 显示额外信息
                    if 'description' in model and model['description']:
                        print(f"     描述: {model['description'][:50]}...")
                    if 'owned_by' in model:
                        print(f"     提供方: {model['owned_by']}")
                    if 'created' in model:
                        import datetime
                        created_time = datetime.datetime.fromtimestamp(model['created'])
                        print(f"     创建时间: {created_time.strftime('%Y-%m-%d')}")
                    print()
                
                if len(models) > 3:
                    print(f"  ... 还有 {len(models) - 3} 个模型")
                
            else:
                print(f"❌ 未获取到任何模型")
                
        except Exception as e:
            print(f"❌ 获取失败: {e}")

def test_cache_functionality():
    """测试缓存功能"""
    print("\n🧪 测试缓存功能")
    print("=" * 50)
    
    # 找一个已配置的供应商
    test_provider = None
    for provider in LLMProvider:
        api_key = llm_config_manager.get_api_key(provider)
        if api_key:
            test_provider = provider
            break
    
    if not test_provider:
        print("⚠️  没有配置任何API密钥，跳过缓存测试")
        return
    
    print(f"🔍 使用 {test_provider.value} 测试缓存功能")
    
    # 清除缓存
    llm_config_manager.clear_model_cache(test_provider)
    print("🗑️  已清除缓存")
    
    # 第一次获取（实时）
    print("\n📡 第一次获取（实时）...")
    start_time = time.time()
    models1 = llm_config_manager.get_available_models(test_provider, use_cache=False)
    duration1 = time.time() - start_time
    print(f"✅ 获取 {len(models1)} 个模型，耗时: {duration1:.2f}秒")
    
    # 第二次获取（使用缓存）
    print("\n💾 第二次获取（使用缓存）...")
    start_time = time.time()
    models2 = llm_config_manager.get_available_models(test_provider, use_cache=True)
    duration2 = time.time() - start_time
    print(f"✅ 获取 {len(models2)} 个模型，耗时: {duration2:.2f}秒")
    
    # 比较结果
    if len(models1) == len(models2):
        print("✅ 缓存结果与实时结果一致")
        print(f"⚡ 缓存加速: {duration1/duration2:.1f}x 倍")
    else:
        print("❌ 缓存结果与实时结果不一致")
    
    # 测试缓存过期
    print(f"\n⏰ 等待缓存过期（{llm_config_manager.CACHE_DURATION}秒）...")
    print("（实际测试中可以修改CACHE_DURATION为较小值）")

def test_model_comparison():
    """测试不同供应商的模型对比"""
    print("\n🧪 测试模型对比功能")
    print("=" * 50)
    
    all_models = {}
    
    for provider in LLMProvider:
        api_key = llm_config_manager.get_api_key(provider)
        if not api_key:
            continue
        
        try:
            models = llm_config_manager.get_available_models(provider)
            all_models[provider.value] = models
            print(f"✅ {provider.value}: {len(models)} 个模型")
        except Exception as e:
            print(f"❌ {provider.value}: 获取失败 - {e}")
    
    if not all_models:
        print("⚠️  没有成功获取任何供应商的模型")
        return
    
    # 统计信息
    print("\n📊 模型统计:")
    total_models = sum(len(models) for models in all_models.values())
    print(f"总模型数: {total_models}")
    
    # 成本分析
    print("\n💰 成本分析:")
    all_costs = []
    for provider, models in all_models.items():
        costs = [m['cost_per_1k'] for m in models]
        if costs:
            min_cost = min(costs)
            max_cost = max(costs)
            avg_cost = sum(costs) / len(costs)
            print(f"{provider}: ${min_cost:.6f} - ${max_cost:.6f} (平均: ${avg_cost:.6f})")
            all_costs.extend(costs)
    
    if all_costs:
        print(f"全局最低成本: ${min(all_costs):.6f}/1K")
        print(f"全局最高成本: ${max(all_costs):.6f}/1K")
    
    # Token限制分析
    print("\n🔢 Token限制分析:")
    all_tokens = []
    for provider, models in all_models.items():
        tokens = [m['max_tokens'] for m in models]
        if tokens:
            min_tokens = min(tokens)
            max_tokens = max(tokens)
            print(f"{provider}: {min_tokens:,} - {max_tokens:,} tokens")
            all_tokens.extend(tokens)
    
    if all_tokens:
        print(f"全局最大Token数: {max(all_tokens):,}")

def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理")
    print("=" * 50)
    
    # 测试无效API密钥
    print("🔍 测试无效API密钥处理...")
    
    # 临时设置无效密钥
    original_key = os.getenv('OPENAI_API_KEY')
    os.environ['OPENAI_API_KEY'] = 'invalid-key-test'
    
    try:
        models = llm_config_manager.get_available_models(LLMProvider.OPENAI)
        print("❌ 应该抛出异常但没有")
    except Exception as e:
        print(f"✅ 正确处理了无效API密钥: {type(e).__name__}")
    finally:
        # 恢复原始密钥
        if original_key:
            os.environ['OPENAI_API_KEY'] = original_key
        else:
            os.environ.pop('OPENAI_API_KEY', None)
    
    # 测试网络错误处理
    print("\n🔍 测试网络错误处理...")
    
    # 设置无效的base URL
    original_base_url = os.getenv('OPENAI_BASE_URL')
    os.environ['OPENAI_BASE_URL'] = 'https://invalid-url-test.com'
    
    try:
        models = llm_config_manager.get_available_models(LLMProvider.OPENAI)
        print("❌ 应该抛出异常但没有")
    except Exception as e:
        print(f"✅ 正确处理了网络错误: {type(e).__name__}")
    finally:
        # 恢复原始URL
        if original_base_url:
            os.environ['OPENAI_BASE_URL'] = original_base_url
        else:
            os.environ.pop('OPENAI_BASE_URL', None)

def main():
    """主测试函数"""
    print("🤖 实时模型获取功能测试")
    print("=" * 60)
    
    try:
        # 基础实时获取测试
        test_realtime_model_fetching()
        
        # 缓存功能测试
        test_cache_functionality()
        
        # 模型对比测试
        test_model_comparison()
        
        # 错误处理测试
        test_error_handling()
        
        print("\n" + "=" * 60)
        print("🎉 实时模型获取功能测试完成！")
        
        print("\n💡 使用建议:")
        print("1. 在Web界面中点击'🔄 刷新模型'获取最新模型列表")
        print("2. 使用'📊 模型列表管理'查看所有供应商的模型统计")
        print("3. 缓存机制可以提高响应速度，避免频繁API调用")
        print("4. 如果模型列表异常，可以清除缓存重新获取")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
