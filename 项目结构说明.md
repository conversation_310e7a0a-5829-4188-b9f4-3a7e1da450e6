# 智能发布计划助手 - 项目结构说明

## 📁 项目目录结构

```
release/                              # 智能发布计划助手主目录
├── src/                             # 核心代码目录
│   ├── __init__.py                  # Python包初始化文件
│   ├── database.py                  # 数据库连接和查询模块
│   ├── jenkins_client.py            # Jenkins集成模块
│   ├── agent_workflow.py            # LangGraph工作流设计
│   ├── release_agent.py             # Agent核心逻辑实现
│   └── llm_config.py                # LLM配置管理模块
├── app.py                           # Streamlit Web界面主程序
├── run.py                           # 应用启动脚本
├── demo.py                          # 功能演示脚本
├── test_agent.py                    # 功能测试脚本
├── test_llm.py                      # LLM功能测试脚本
├── requirements.txt                 # Python依赖包列表
├── .env.example                     # 环境配置示例文件
├── release-helper.md                # 原始需求文档（提示词文件）
├── README.md                        # 项目详细文档
├── PROJECT_SUMMARY.md               # 项目总结文档
├── QUICK_START.md                   # 快速启动指南
├── LLM_CONFIG_GUIDE.md              # LLM配置指南
├── DEPENDENCIES_UPDATE.md           # 依赖更新说明
└── 项目结构说明.md                   # 本文件
```

## 📋 文件功能说明

### 核心代码模块 (src/)

#### database.py
- **功能**: MySQL数据库连接和查询
- **主要类**:
  - `DatabaseManager`: 数据库连接管理
  - `ReleaseQueryService`: 发布计划查询服务
- **核心功能**:
  - 多租户服务查询
  - 单租户部署计划查询
  - 发布计划整合

#### jenkins_client.py
- **功能**: Jenkins API集成
- **主要类**:
  - `JenkinsClient`: Jenkins服务器交互
  - `JenkinsJobGenerator`: Jenkins job生成器
- **核心功能**:
  - Job触发和参数传递
  - 执行状态监控
  - 控制台输出获取
  - 支持11种服务的job配置

#### agent_workflow.py
- **功能**: LangGraph工作流设计
- **主要类**:
  - `WorkflowState`: 工作流状态模型
  - `ReleaseAgentWorkflow`: 发布代理工作流
- **核心功能**:
  - 状态机设计
  - 用户交互节点
  - 条件分支控制
  - 错误处理机制

#### release_agent.py
- **功能**: Agent核心逻辑
- **主要类**:
  - `ReleaseAgent`: 智能发布计划助手
- **核心功能**:
  - 用户输入解析（正则+LLM双重解析）
  - 发布计划格式化
  - 状态管理
  - 文件提示词支持

#### llm_config.py
- **功能**: LLM配置管理
- **主要类**:
  - `LLMConfigManager`: LLM配置管理器
  - `LLMProvider`: 供应商枚举
  - `LLMModel`: 模型配置数据类
- **核心功能**:
  - 多供应商支持（OpenAI、Google、Anthropic）
  - 动态模型切换
  - 连接测试
  - LLM实例创建

### 应用程序文件

#### app.py
- **功能**: Streamlit Web界面
- **主要功能**:
  - 发布计划查询界面
  - 计划审批界面
  - 执行管理界面
  - 监控面板界面

#### run.py
- **功能**: 应用启动脚本
- **主要功能**:
  - 依赖检查
  - 环境配置检查
  - Streamlit应用启动

### 测试和演示文件

#### demo.py
- **功能**: 功能演示脚本
- **演示内容**:
  - 版本号解析演示
  - 发布计划格式化演示
  - Jenkins job生成演示

#### test_agent.py
- **功能**: 功能测试脚本
- **测试内容**:
  - 环境配置测试
  - 版本号解析测试
  - Jenkins job生成测试
  - 模拟部署计划测试
  - Agent工作流测试

#### test_llm.py
- **功能**: LLM功能测试脚本
- **测试内容**:
  - LLM配置管理器测试
  - 多供应商连接测试
  - 智能解析功能测试
  - 模型切换功能测试

### 配置和文档文件

#### requirements.txt
- **功能**: Python依赖包列表
- **主要依赖**:
  - langchain == 0.3.26
  - langgraph == 0.5.0
  - streamlit == 1.41.0
  - mysql-connector-python == 9.3.0
  - python-jenkins == 1.8.2
  - google-generativeai == 0.8.3
  - langchain-openai == 0.2.10
  - langchain-google-genai == 2.0.8
  - langchain-anthropic == 0.3.0

#### .env.example
- **功能**: 环境配置示例
- **配置项**:
  - MySQL数据库配置
  - Jenkins服务器配置
  - LLM API配置（可选）

#### release-helper.md
- **功能**: 原始需求文档
- **作用**: 作为Agent的提示词文件使用

## 🚀 使用流程

### 1. 快速体验
```bash
cd release
python demo.py          # 查看功能演示
python test_agent.py    # 运行功能测试
python test_llm.py      # 测试LLM功能
```

### 2. 完整部署
```bash
cd release
pip install -r requirements.txt    # 安装依赖
cp .env.example .env               # 配置环境
python run.py                      # 启动Web界面
```

### 3. Web界面访问
- 本地访问: http://localhost:8501
- 功能: 发布计划查询、审批、执行管理、监控、LLM配置

## 🔧 技术架构

### 技术栈
- **AI框架**: LangChain + LangGraph
- **Web框架**: Streamlit
- **数据库**: MySQL
- **CI/CD**: Jenkins
- **开发语言**: Python 3.8+

### 设计模式
- **模块化设计**: 各功能模块独立
- **状态机模式**: LangGraph工作流
- **工厂模式**: Jenkins job生成
- **观察者模式**: 执行状态监控

## 📊 支持的服务

系统支持以下11种服务的自动化部署：
1. aries, 2. canis, 3. em, 4. openlog, 5. pisces
6. chinacrm, 7. lumos, 8. taurus, 9. rigel, 10. hydra, 11. mintaka

## 🎯 核心特性

- ✅ 智能版本号解析（正则+LLM双重解析）
- ✅ 多LLM供应商支持（OpenAI、Google、Anthropic）
- ✅ 动态LLM配置和模型切换
- ✅ 多租户/单租户服务支持
- ✅ 用户审批和确认流程
- ✅ 实时Jenkins job监控
- ✅ 模块化和可扩展设计
- ✅ 完整的错误处理机制

---

**注意**: 这是一个原型验证系统，在生产环境使用前请进行充分测试和配置。
