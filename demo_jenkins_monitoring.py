#!/usr/bin/env python3
"""
Jenkins实时监控功能演示
展示持续监控、自动刷新和最新20行日志显示功能
"""

import streamlit as st
import time
import random
from datetime import datetime

# 页面配置
st.set_page_config(
    page_title="Jenkins实时监控演示",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

def mock_jenkins_status(job_name: str, build_number: int, elapsed_time: int):
    """模拟Jenkins job状态"""
    # 模拟不同阶段的状态
    if elapsed_time < 30:
        status = "RUNNING"
        building = True
        duration = 0
    elif elapsed_time < 60:
        status = "RUNNING" 
        building = True
        duration = 0
    else:
        # 随机成功或失败
        status = "SUCCESS" if random.random() > 0.3 else "FAILURE"
        building = False
        duration = elapsed_time * 1000  # 转换为毫秒
    
    return {
        "success": True,
        "job_name": job_name,
        "build_number": build_number,
        "status": status,
        "building": building,
        "duration": duration,
        "url": f"https://jenkins.example.com/job/{job_name}/{build_number}/"
    }

def mock_console_output(job_name: str, build_number: int, elapsed_time: int):
    """模拟控制台输出"""
    # 模拟日志行
    log_lines = [
        f"[{datetime.now().strftime('%H:%M:%S')}] Started by user admin",
        f"[{datetime.now().strftime('%H:%M:%S')}] Building in workspace /var/jenkins_home/workspace/{job_name}",
        f"[{datetime.now().strftime('%H:%M:%S')}] Checking out code from repository...",
        f"[{datetime.now().strftime('%H:%M:%S')}] Running pre-build scripts...",
        f"[{datetime.now().strftime('%H:%M:%S')}] Compiling source code...",
        f"[{datetime.now().strftime('%H:%M:%S')}] Running unit tests...",
        f"[{datetime.now().strftime('%H:%M:%S')}] Generating test reports...",
        f"[{datetime.now().strftime('%H:%M:%S')}] Building Docker image...",
        f"[{datetime.now().strftime('%H:%M:%S')}] Pushing to registry...",
        f"[{datetime.now().strftime('%H:%M:%S')}] Deploying to staging environment...",
        f"[{datetime.now().strftime('%H:%M:%S')}] Running integration tests...",
        f"[{datetime.now().strftime('%H:%M:%S')}] Validating deployment...",
        f"[{datetime.now().strftime('%H:%M:%S')}] Sending notifications...",
        f"[{datetime.now().strftime('%H:%M:%S')}] Cleaning up temporary files...",
        f"[{datetime.now().strftime('%H:%M:%S')}] Archiving artifacts...",
    ]
    
    # 根据经过时间添加更多日志
    current_logs = []
    lines_per_10_sec = 3
    max_lines = min(len(log_lines), (elapsed_time // 10) * lines_per_10_sec + 5)
    
    for i in range(max_lines):
        if i < len(log_lines):
            current_logs.append(log_lines[i])
        else:
            # 添加一些动态日志
            current_logs.append(f"[{datetime.now().strftime('%H:%M:%S')}] Processing step {i-len(log_lines)+1}...")
    
    # 如果超过20行，只返回最新20行
    if len(current_logs) > 20:
        current_logs = current_logs[-20:]
    
    return {
        "success": True,
        "console_output": "\n".join(current_logs)
    }

def monitoring_dashboard_demo():
    """监控面板演示"""
    st.header("📊 Jenkins Job 实时监控演示")
    
    # 初始化session state
    if 'demo_monitoring_job' not in st.session_state:
        st.session_state.demo_monitoring_job = None
    if 'demo_auto_refresh' not in st.session_state:
        st.session_state.demo_auto_refresh = False
    if 'demo_start_time' not in st.session_state:
        st.session_state.demo_start_time = None
    
    # Job输入区域
    st.subheader("🔍 Job配置")
    col1, col2, col3 = st.columns([3, 2, 2])
    
    with col1:
        job_name = st.text_input("Job名称", value="demo-deployment-job", key="demo_job_name")
    
    with col2:
        build_number = st.number_input("Build号", min_value=1, value=123, key="demo_build_number")
    
    with col3:
        st.write("") # 空行对齐
        auto_refresh = st.checkbox("🔄 自动刷新 (5秒)", value=st.session_state.demo_auto_refresh)
        st.session_state.demo_auto_refresh = auto_refresh
    
    # 控制按钮
    col1, col2, col3 = st.columns([2, 2, 2])
    
    with col1:
        if st.button("🚀 开始监控", type="primary", key="demo_start"):
            if job_name:
                st.session_state.demo_monitoring_job = {
                    "job_name": job_name,
                    "build_number": build_number
                }
                st.session_state.demo_start_time = time.time()
                st.success(f"✅ 开始监控 {job_name} Build {build_number}")
            else:
                st.error("请输入Job名称")
    
    with col2:
        if st.button("🔄 手动刷新", key="demo_refresh"):
            if st.session_state.demo_monitoring_job:
                st.success("🔄 数据已刷新")
                st.rerun()
            else:
                st.warning("请先开始监控一个Job")
    
    with col3:
        if st.button("⏹️ 停止监控", key="demo_stop"):
            st.session_state.demo_monitoring_job = None
            st.session_state.demo_auto_refresh = False
            st.session_state.demo_start_time = None
            st.success("⏹️ 已停止监控")
    
    # 显示监控结果
    if st.session_state.demo_monitoring_job:
        display_demo_monitoring()
        
        # 自动刷新逻辑
        if st.session_state.demo_auto_refresh:
            time.sleep(0.1)
            st.rerun()
    else:
        st.info("💡 请输入Job名称和Build号，然后点击'开始监控'来监控Jenkins job的执行状态")

def display_demo_monitoring():
    """显示演示监控结果"""
    job_info = st.session_state.demo_monitoring_job
    
    if not job_info:
        return
    
    # 计算经过时间
    elapsed_time = int(time.time() - st.session_state.demo_start_time) if st.session_state.demo_start_time else 0
    
    st.divider()
    st.subheader(f"📊 {job_info['job_name']} Build {job_info['build_number']}")
    
    # 显示最后更新时间
    st.caption(f"最后更新: {datetime.now().strftime('%H:%M:%S')} | 运行时间: {elapsed_time}秒")
    
    # 获取模拟数据
    status_info = mock_jenkins_status(job_info['job_name'], job_info['build_number'], elapsed_time)
    console_info = mock_console_output(job_info['job_name'], job_info['build_number'], elapsed_time)
    
    # 状态信息
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        status = status_info.get("status", "未知")
        if status == "SUCCESS":
            st.success(f"✅ {status}")
        elif status == "FAILURE":
            st.error(f"❌ {status}")
        elif status == "RUNNING":
            st.info(f"🔄 {status}")
        else:
            st.warning(f"⚠️ {status}")
    
    with col2:
        building = status_info.get("building", False)
        if building:
            st.info("🔄 构建中")
        else:
            st.success("✅ 已完成")
    
    with col3:
        duration = status_info.get("duration", 0)
        if duration > 0:
            duration_sec = duration / 1000
            st.metric("持续时间", f"{duration_sec:.1f}秒")
        else:
            st.metric("持续时间", f"{elapsed_time}秒")
    
    with col4:
        url = status_info.get("url", "")
        if url:
            st.markdown(f"[🔗 查看详情]({url})")
    
    # 控制台输出 (最新20行)
    st.subheader("📝 最新日志 (最近20行)")
    
    console_output = console_info.get("console_output", "")
    if console_output:
        # 使用代码块显示日志，支持滚动
        st.code(console_output, language="text", line_numbers=True)
    else:
        st.info("暂无日志输出")
    
    # 自动刷新状态指示
    if st.session_state.demo_auto_refresh:
        st.info("🔄 自动刷新已启用 (每5秒刷新一次)")
    else:
        st.info("⏸️ 自动刷新已暂停")

def main():
    """主函数"""
    st.title("🚀 Jenkins实时监控功能演示")
    st.markdown("展示持续监控、自动刷新和最新20行日志显示功能")
    
    # 功能说明
    with st.expander("📖 功能说明", expanded=False):
        st.markdown("""
        ### ✨ 主要功能：
        
        1. **实时监控**: 监控Jenkins job的执行状态
        2. **自动刷新**: 每5秒自动刷新数据
        3. **手动刷新**: 随时手动刷新最新状态
        4. **最新日志**: 只显示最新的20行控制台输出
        5. **状态展示**: 清晰显示job状态、构建状态、持续时间等
        
        ### 🎯 使用方法：
        
        1. 输入Job名称和Build号
        2. 点击"开始监控"开始监控
        3. 可选择启用"自动刷新"进行实时更新
        4. 使用"手动刷新"按钮随时更新数据
        5. 点击"停止监控"结束监控
        
        ### 💡 特性：
        
        - 📊 实时状态显示
        - 🔄 自动/手动刷新
        - 📝 最新20行日志
        - ⏱️ 运行时间统计
        - 🔗 Jenkins链接跳转
        """)
    
    monitoring_dashboard_demo()

if __name__ == "__main__":
    main()
